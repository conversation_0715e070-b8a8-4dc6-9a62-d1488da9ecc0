package SpringBoot.service

import com.sankuai.gaigc.arrange.base.SpringBootSpecification
import com.sankuai.gaigc.arrange.common.core.bot.service.impl.AIBotSnapshotServiceImpl
import com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO
import com.sankuai.gaigc.arrange.dao.dal.example.AIBotSnapshotDOExample
import com.sankuai.gaigc.arrange.dao.dal.mapper.AIBotSnapshotDOMapper
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Requires

@Requires({ System.getProperty("os.name").contains("Mac OS X") })
class AIBotSnapshotServiceImplSpringBootSpec extends SpringBootSpecification {
    @Autowired
    AIBotSnapshotDOMapper aiBotSnapshotDOMapper
    @Autowired
    AIBotSnapshotServiceImpl aiBotSnapshotService

    def selectBotSnapShotByIdAndVersion() {
        given:
        def botId = 32L
//        def version = 1
        def version = null
//        def version = 23
//        def version = 0
        when:
        // 新接口
        def result = aiBotSnapshotService.selectBotSnapShotByIdAndVersion(botId, version)
        log.info("result:{} version: {}", result.getId(), result.getVersion())
        //老接口
        def resultOld = this.selectBotSnapShotByIdAndVersionOld(botId, version)
        log.info("resultOlb:{} version: {}", resultOld.getId(), result.getVersion())
        then:
        result.getId() != null
        resultOld.getId() != null
        result.getId() == resultOld.getId()
    }


    /**
     * 老逻辑
     */
    private AIBotSnapshotDO selectBotSnapShotByIdAndVersionOld(Long botId, Integer version) {
        AIBotSnapshotDOExample example = new AIBotSnapshotDOExample();
        AIBotSnapshotDOExample.Criteria criteria = example.createCriteria();
        criteria.andBotIdEqualTo(botId);

        List<AIBotSnapshotDO> botSnapshotDOS = null;
        if (version != null) {
            if (version == 0) {
                //版本号为0，按版本号倒序查最新版本
                example.setOrderByClause("version desc");
            } else {
                //版本不为null切不为0，直接查对应版本
                criteria.andVersionEqualTo(version);
            }
            botSnapshotDOS = aiBotSnapshotDOMapper.selectByExampleWithBLOBs(example);
        } else {
            //版本为null，则直接查发布上线版本
            criteria.andStatusEqualTo(1);
            botSnapshotDOS = aiBotSnapshotDOMapper.selectByExampleWithBLOBs(example);
        }
        if (CollectionUtils.isEmpty(botSnapshotDOS)) return null;
        return botSnapshotDOS.get(0);
    }


}
