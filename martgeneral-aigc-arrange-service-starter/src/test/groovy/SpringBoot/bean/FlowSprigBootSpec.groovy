package SpringBoot.bean

import com.alibaba.fastjson.JSON
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.Gson
import com.sankuai.gaigc.arrange.base.SpringBootSpecification
import com.sankuai.gaigc.arrange.common.core.promptflow.beans.PromptFlowManager
import com.sankuai.gaigc.arrange.dao.dal.example.AigcFlowSnapshotExample
import com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFlowSnapshotMapper
import org.apache.commons.lang.ObjectUtils
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Requires

import javax.annotation.Resource

@Requires({ System.getProperty("os.name").contains("Mac OS X") })
class FlowSprigBootSpec extends SpringBootSpecification {
    @Autowired
    private PromptFlowManager promptFlowManager;
    @Resource
    private AigcFlowSnapshotMapper aigcFlowSnapshotMapper

    def deepCopy() {
        given: "all flows  status  is 1"
        AigcFlowSnapshotExample aigcFlowSnapshotExample = new AigcFlowSnapshotExample();
        aigcFlowSnapshotExample.createCriteria().andStatusEqualTo(1);
        when: " already deep copy"
        def flowSnapshots = aigcFlowSnapshotMapper.selectByExampleWithBLOBs(aigcFlowSnapshotExample)
        for (final def flow in flowSnapshots) {
            def source = promptFlowManager.getFlowById(flow.getFlowId())
            def target = source.deepCopy()


            // fastjson
            def string = JSON.toJSONString(source)
            def string1 = JSON.toJSONString(target)
            if (ObjectUtils.notEqual(string, string1)) {
                log.info("string.fastJson:{}", string)
                log.info("string1.fastJson:{}", string1)
                throw new RuntimeException("fastJson 序列化结果不一致")
            }

            // 使用 Jackson 序列化
            ObjectMapper objectMapper = new ObjectMapper()
            String jacksonSourceJson = objectMapper.writeValueAsString(source)
            String jacksonTargetJson = objectMapper.writeValueAsString(target)
            if (ObjectUtils.notEqual(jacksonSourceJson, jacksonTargetJson)) {
                log.info("string.Jackson:{}", string)
                log.info("string1.Jackson:{}", string1)
                throw new RuntimeException("Jackson 序列化结果不一致")
            }

            // 使用 Gson 序列化
            Gson gson = new Gson()
            String gsonSourceJson = gson.toJson(source)
            String gsonTargetJson = gson.toJson(target)
            if (ObjectUtils.notEqual(gsonSourceJson, gsonTargetJson)) {
                log.info("string.gson:{}", string)
                log.info("string1.gson:{}", string1)
                throw new RuntimeException("gson 序列化结果不一致")
            }

            log.info("deep copy success  ,flowId:{}", flow.getFlowId())
        }
        then:
        flowSnapshots != null
    }
}
