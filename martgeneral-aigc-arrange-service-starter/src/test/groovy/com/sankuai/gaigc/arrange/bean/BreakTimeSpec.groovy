package com.sankuai.gaigc.arrange.bean

import com.sankuai.gaigc.arrange.api.entity.audio.BreakTime
import spock.lang.Specification

class BreakTimeSpec extends Specification {

    def 基本测试() {
        given:
        def time = new BreakTime()
        def time1 = time.getStartBreakTime()
        def time2 = time.getEndBreakTime()

        def time2333 = new BreakTime(time1, time2)
        time.setStartBreakTime(time1)
        time.setEndBreakTime(time2)
        when:
        def string = time.toString()

        then:
        string != null
    }
}

