package com.sankuai.gaigc.arrange.common.core.service.mapper

import com.sankuai.gaigc.arrange.common.core.bot.service.AigcFlowCostCollectRecordService
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.aigc.flow.cost.collect.QueryByFlowIdAppIdModelNameDTO
import com.sankuai.gaigc.arrange.dao.dal.entity.AigcFlowCostCollectRecordDO
import com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFlowCostCollectRecordDOMapper
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.web.WebAppConfiguration
import spock.lang.Requires
import spock.lang.Specification
import spock.lang.Unroll

import javax.annotation.Resource

@WebAppConfiguration
@SpringBootTest
@AutoConfigureMockMvc
class AigcFlowCostCollectRecordDOMapperSpringBootSpec extends Specification {
    @Resource
    private AigcFlowCostCollectRecordDOMapper aigcFlowCostCollectRecordDOMapper

    @Resource
    private AigcFlowCostCollectRecordService aigcFlowCostCollectRecordService

    @Unroll
    @Requires({ System.getProperty("os.name").contains("Mac OS X") })
    def "测试batchUpdateByPrimaryKey和batchInsert"() {
        given:
        List<AigcFlowCostCollectRecordDO> list = new ArrayList<>()
        list.add(new AigcFlowCostCollectRecordDO(flowId: 1,
                appId: 2, modelName: "张俊杰测试", inputUsage: 11, outputUsage: 22, cost: "333"
                , isDel: 0
        ))

        list.add(new AigcFlowCostCollectRecordDO(flowId: 12,
                appId: 332, modelName: "张俊杰测试2", inputUsage: 11, outputUsage: 22, cost: "333"
                , isDel: 0
        ))
        List<Long> flowIdList = new ArrayList<>();
        List<Long> appIdList = new ArrayList<>();
        List<String> modelNameList = new ArrayList<>();

        for (final def temp in list) {
            flowIdList.add(temp.getFlowId())
            appIdList.add(temp.getAppId())
            modelNameList.add(temp.getModelName())
        }


        QueryByFlowIdAppIdModelNameDTO queryByFlowIdAppIdModelNameDTO = new QueryByFlowIdAppIdModelNameDTO(flowIdList: flowIdList, appIdList: appIdList, modelNameList: modelNameList)
        List<AigcFlowCostCollectRecordDO> queryByFlowIdAppIdModelName = aigcFlowCostCollectRecordService.queryByFlowIdAppIdModelName(queryByFlowIdAppIdModelNameDTO)
        for (final def temp in queryByFlowIdAppIdModelName) {
            aigcFlowCostCollectRecordDOMapper.deleteByPrimaryKey(temp.getId())
        }
        when:
        int insert = aigcFlowCostCollectRecordDOMapper.batchInsert(list)
        int update = aigcFlowCostCollectRecordDOMapper.batchUpdateByPrimaryKey(list)

        then:
        insert != 0
        update != 0
    }

}