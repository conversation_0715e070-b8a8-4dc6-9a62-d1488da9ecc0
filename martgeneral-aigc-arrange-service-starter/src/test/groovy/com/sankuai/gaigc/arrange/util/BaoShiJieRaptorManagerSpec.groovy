package com.sankuai.gaigc.arrange.util

import com.sankuai.gaigc.arrange.common.core.monitor.BaoShiJieSecCheckContentRangeRaptor
import com.sankuai.gaigc.arrange.common.enums.AppEntityTypeEnum
import com.sankuai.gaigc.arrange.common.core.promptflow.remote.baoshijie.BaoShiJieAuditResult
import spock.lang.Specification

class BaoShiJieRaptorManagerSpec extends Specification {

    def countAuditContentToTransaction() {
        given:
        AppEntityTypeEnum appEntityTypeEnum = AppEntityTypeEnum.FLOW
        Long entityId = 3331L
        Integer content = 11111
        BaoShiJieAuditResult baoShiJieAuditResult = new BaoShiJieAuditResult()
        baoShiJieAuditResult.isHaveRiskWords()
        when:
        def content1 = BaoShiJieSecCheckContentRangeRaptor.recordRange("ready_send", appEntityTypeEnum, entityId, content)
        then:
        content1 == null
    }

//    def testBuildNameAndType() {
//        given:
//        true
//        expect:
//        def resultName = BaoShiJieSecCheckContentRangeRaptor.buildName(appEntityTypeEnum, entityId)
//        def resultType = BaoShiJieSecCheckContentRangeRaptor.buildType(appEntityTypeEnum, contentCount)
//        result == resultType + "---" + resultName
//
//        where:
//        appEntityTypeEnum      | entityId | contentCount | result
//        AppEntityTypeEnum.FLOW | 111L     | 0            | "baoshijie_flow_0-256---flowId:111"
//        AppEntityTypeEnum.FLOW | 111L     | 256          | "baoshijie_flow_0-256---flowId:111"
//        AppEntityTypeEnum.FLOW | 555L     | 256          | "baoshijie_flow_0-256---flowId:555"
//        AppEntityTypeEnum.FLOW | 6111L    | 256          | "baoshijie_flow_0-256---flowId:6111"
//        AppEntityTypeEnum.FLOW | 6111L    | 1111         | "baoshijie_flow_1000-2000---flowId:6111"
//        AppEntityTypeEnum.FLOW | 333L     | 1111         | "baoshijie_flow_1000-2000---flowId:333"
//        AppEntityTypeEnum.FLOW | 6111L    | 2566         | "baoshijie_flow_2000-5000---flowId:6111"
//        AppEntityTypeEnum.FLOW | 6111L    | 4444         | "baoshijie_flow_2000-5000---flowId:6111"
//        AppEntityTypeEnum.FLOW | 6111L    | 8888         | "baoshijie_flow_5000-10000---flowId:6111"
//        AppEntityTypeEnum.FLOW | 6111L    | 15555        | "baoshijie_flow_10000-20000---flowId:6111"
//        AppEntityTypeEnum.FLOW | 6111L    | 20000        | "baoshijie_flow_10000-20000---flowId:6111"
//        AppEntityTypeEnum.FLOW | 6111L    | 88888        | "baoshijie_flow_20000-20000+---flowId:6111"
//        AppEntityTypeEnum.FLOW | 6111L    | 20001        | "baoshijie_flow_20000-20000+---flowId:6111"
//
//
//        AppEntityTypeEnum.BOT  | 111L     | 0            | "baoshijie_bot_0-256---botId:111"
//        AppEntityTypeEnum.BOT  | 111L     | 256          | "baoshijie_bot_0-256---botId:111"
//        AppEntityTypeEnum.BOT  | 555L     | 256          | "baoshijie_bot_0-256---botId:555"
//        AppEntityTypeEnum.BOT  | 6111L    | 256          | "baoshijie_bot_0-256---botId:6111"
//        AppEntityTypeEnum.BOT  | 6111L    | 1111         | "baoshijie_bot_1000-2000---botId:6111"
//        AppEntityTypeEnum.BOT  | 333L     | 1111         | "baoshijie_bot_1000-2000---botId:333"
//        AppEntityTypeEnum.BOT  | 6111L    | 2566         | "baoshijie_bot_2000-5000---botId:6111"
//        AppEntityTypeEnum.BOT  | 6111L    | 4444         | "baoshijie_bot_2000-5000---botId:6111"
//        AppEntityTypeEnum.BOT  | 6111L    | 8888         | "baoshijie_bot_5000-10000---botId:6111"
//        AppEntityTypeEnum.BOT  | 6111L    | 15555        | "baoshijie_bot_10000-20000---botId:6111"
//        AppEntityTypeEnum.BOT  | 6111L    | 20000        | "baoshijie_bot_10000-20000---botId:6111"
//        AppEntityTypeEnum.BOT  | 6111L    | 88888        | "baoshijie_bot_20000-20000+---botId:6111"
//        AppEntityTypeEnum.BOT  | 6111L    | 20001        | "baoshijie_bot_20000-20000+---botId:6111"
//
//
//    }

    def length() {
        given:
        true
        expect:
        String content2 = content
        content2.length() == ssd

        where:
        content    | ssd
        "asdd"     | 4
        "asd,"     | 4
        "asd "     | 4

        "as1d "    | 5
        "1111"     | 4
        "奥赛奥赛" | 4

    }

}
