package com.sankuai.gaigc.arrange.bean

import com.sankuai.gaigc.arrange.api.entity.UserVO
import spock.lang.Specification

class UserVOSpec extends Specification {

    def 构造() {
        given:
        def o = new UserVO(userId: 1, userName: "zhangjunjie", userType: 1)
        when:
        def id = o.getUserId()
        def name = o.getUserName()
        def type = o.getUserType()

        then:
        o != null
    }
}
