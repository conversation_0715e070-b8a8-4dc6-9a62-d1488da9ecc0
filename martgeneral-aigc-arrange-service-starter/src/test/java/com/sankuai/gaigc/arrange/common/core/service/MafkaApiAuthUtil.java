package com.sankuai.gaigc.arrange.common.core.service;

import org.apache.commons.codec.binary.Base64;

import java.security.MessageDigest;

/**
 * https://km.sankuai.com/collabpage/1700748980
 */
public class MafkaApiAuthUtil {
    /**
     * https://mafka.mws-test.sankuai.com/tool/api_manage
     *
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {
        // 请求的url，不包括域名。例如：/api/queryTopic,注意url最前面需要加/
        System.out.println(getSign("2n8srvz9k48tg8tk2j98qqchpwvk2f8c", "/api/consumer/broadcast/create"));
    }

    /**
     * 生成签名
     *
     * @param secretKey 秘钥，由MQ团队提供 具体在这里看: https://mafka.mws-test.sankuai.com/tool/api_manage
     * @param uri       请求的url，不包括域名。例如：/api/queryTopic,注意url最前面需要加/
     * @return 签名信息
     * @throws Exception
     */
    private static String getSign(String secretKey, String uri) throws Exception {
        String stringToSign = secretKey + uri;
        String sign = "";
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(stringToSign.getBytes("UTF-8"));
            sign = Base64.encodeBase64String(messageDigest.digest());
        } catch (Exception e) {
            // 业务可自行修改
            throw new Exception();
        }
        return sign;
    }

}
