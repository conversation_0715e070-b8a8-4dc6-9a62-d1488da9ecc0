<?xml version="1.0" encoding="UTF-8"?>
<configuration status="info">
    <appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} %t %-5level %logger{30}.%method - %ex %msg%n" />
        </Console>

        <!--默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，非阻塞模式-->
        <XMDFile name="infoAppender" fileName="info.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <Filters>
                <ThresholdFilter level="warn" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <XMDFile name="warnAppender" fileName="warn.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <XMDFile name="errorAppender" fileName="error.log" sizeBasedTriggeringSize="512M"
                 rolloverMax="30">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>

        <!--日志远程上报-->
        <AsyncScribe name="ScribeAsyncAppender" blocking="false">
            <!--远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可自定义scribeCategory属性，scribeCategory优先级高于appkey-->
            <Property name="scribeCategory">com.sankuai.gaigc.arrange.service.componentlog</Property>
            <LcLayout/>
        </AsyncScribe>
        <!--Bot日志-->
        <AsyncScribe name="BotLogAsyncAppender" blocking="false">
            <!--远程日志默认使用appkey作为日志名(app.properties文件中的app.name字段)，也可自定义scribeCategory属性，scribeCategory优先级高于appkey-->
            <Property name="scribeCategory">com.sankuai.gaigc.arrange.service.botlog</Property>
            <LcLayout/>
        </AsyncScribe>
        <!--arrange服务所有的日志的log配置开始-->
        <AsyncScribe name="scribeAsyncAppenderAllLog" blocking="false">
            <Property name="scribeCategory">com.sankuai.gaigc.arrange.service.log</Property>
            <LcLayout/>
        </AsyncScribe>
        <!--arrange服务所有的日志的log配置结束-->
        <!-- horizon日志上报配置 start-->
        <Scribe name="AB-LOG-SERVER">
            <Property name="scribeCategory">horizon_divide_data</Property>
            <LcLayout/>
        </Scribe>
        <Scribe name="AB-LOG-CLIENT">
            <Property name="scribeCategory">horizon_divide_daily</Property>
            <LcLayout/>
        </Scribe>
        <Scribe name="AB-LOG-DIVIDE">
            <Property name="scribeCategory">horizon_divide</Property>
            <LcLayout/>
        </Scribe>
        <Scribe name="AB-LOG-PARAMS">
            <Property name="scribeCategory">com.sankuai.log.horizon-params</Property>
            <LcLayout />
        </Scribe>
        <Scribe name="AB-LOG-BACKUP">
            <Property name="scribeCategory">horizon-divide-backup</Property>
            <LcLayout />
        </Scribe>
        <Scribe name="AB-LOG-DEFAULT">
            <Property name="scribeCategory">horizon-default</Property>
            <LcLayout />
        </Scribe>
        <!-- horizon日志上报配置 end-->

        <CatAppender name="catAppender"/>
        <MDPTraceAppender name="mdpTrace"/>
    </appenders>

    <loggers>
        <!--远程日志，详细使用说明参见 MDP 文档中日志中心部分 https://km.sankuai.com/custom/onecloud/page/424836119#id-3%E6%97%A5%E5%BF%97%E4%B8%AD%E5%BF%83 -->
        <logger name="scribe" level="info" additivity="false">
            <appender-ref ref="ScribeAsyncAppender" />
            <appender-ref ref="scribeAsyncAppenderAllLog"/>
        </logger>
        <logger name="botLog" level="info" additivity="false">
            <appender-ref ref="BotLogAsyncAppender" />
            <appender-ref ref="scribeAsyncAppenderAllLog"/>
        </logger>

        <!-- horizon日志上报配置 start-->
        <logger name="ab_server" level="info" additivity="false">
            <appender-ref ref="AB-LOG-SERVER"/>
        </logger>
        <logger name="ab_client" level="info" additivity="false">
            <appender-ref ref="AB-LOG-CLIENT"/>
        </logger>
        <logger name="ab_divide" level="info" additivity="false">
            <appender-ref ref="AB-LOG-DIVIDE"/>
        </logger>
        <logger name="ab_params" level="info" additivity="false">
            <appender-ref ref="AB-LOG-PARAMS"/>
        </logger>
        <logger name="ab_backup" level="info" additivity="false">
            <appender-ref ref="AB-LOG-BACKUP"/>
        </logger>
        <logger name="ab_default" level="info" additivity="false">
            <appender-ref ref="AB-LOG-DEFAULT"/>
        </logger>
        <!-- horizon日志上报配置 end-->

        <root level="info">
            <appender-ref ref="infoAppender"/>
            <appender-ref ref="warnAppender"/>
            <appender-ref ref="errorAppender"/>
            <appender-ref ref="Console" />
            <appender-ref ref="catAppender"/>
            <appender-ref ref="mdpTrace"/>
            <appender-ref ref="scribeAsyncAppenderAllLog"/>
        </root>
    </loggers>
</configuration>