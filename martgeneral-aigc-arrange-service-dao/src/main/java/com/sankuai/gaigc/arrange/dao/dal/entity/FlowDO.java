package com.sankuai.gaigc.arrange.dao.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: aigc_flow_instance
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class FlowDO {
    /**
     *   字段: id
     *   说明: 自增id
     */
    private Long id;

    /**
     *   字段: type
     *   说明: 组件类型:0-自建 1-复制
     */
    private Integer type;

    /**
     *   字段: name
     *   说明: flow名称
     */
    private String name;

    /**
     *   字段: desc
     *   说明: flow描述
     */
    private String desc;

    /**
     *   字段: app_id
     *   说明: 关联应用id
     */
    private Long appId;

    /**
     *   字段: app_name
     *   说明: 关联应用名
     */
    private String appName;

    /**
     *   字段: scene_id
     *   说明: 关联场景id
     */
    private Long sceneId;

    /**
     *   字段: scene_name
     *   说明: 关联场景名
     */
    private String sceneName;

    /**
     *   字段: biz_id
     *   说明: 关联业务线id，1-到餐 2-到综 3-酒旅
     */
    private Long bizId;

    /**
     *   字段: biz_name
     *   说明: 关联业务线名称
     */
    private String bizName;

    /**
     *   字段: creator
     *   说明: 创建人
     */
    private String creator;

    /**
     *   字段: addtime
     *   说明: 添加时间
     */
    private Date addtime;

    /**
     *   字段: updatetime
     *   说明: 更新时间
     */
    private Date updatetime;

    /**
     *   字段: template_id
     *   说明: 模板ID,不为0时表示通过模板创建
     */
    private Long templateId;
}