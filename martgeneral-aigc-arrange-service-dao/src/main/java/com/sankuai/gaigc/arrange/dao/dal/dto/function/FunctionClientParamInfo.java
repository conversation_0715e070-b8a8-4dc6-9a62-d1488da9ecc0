package com.sankuai.gaigc.arrange.dao.dal.dto.function;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @created: 2024/6/7
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FunctionClientParamInfo {
    /** 一般参数 */
    private List<ParamValueInfo> params;
    /** header参数 */
    private List<ParamValueInfo> headers;
}
