package com.sankuai.gaigc.arrange.dao.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO;
import com.sankuai.gaigc.arrange.dao.dal.example.aigcFunctionDraftDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AigcFunctionDraftDOMapper extends MybatisBLOBsMapper<aigcFunctionDraftDO, aigcFunctionDraftDOExample, Long> {
    int batchInsert(@Param("list") List<aigcFunctionDraftDO> list);
}