package com.sankuai.gaigc.arrange.dao.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AIBotDistributeConfigDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public AIBotDistributeConfigDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public AIBotDistributeConfigDOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public AIBotDistributeConfigDOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public AIBotDistributeConfigDOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdIsNull() {
            addCriterion("fuxi_bot_id is null");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdIsNotNull() {
            addCriterion("fuxi_bot_id is not null");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdEqualTo(Long value) {
            addCriterion("fuxi_bot_id =", value, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdNotEqualTo(Long value) {
            addCriterion("fuxi_bot_id <>", value, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdGreaterThan(Long value) {
            addCriterion("fuxi_bot_id >", value, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdGreaterThanOrEqualTo(Long value) {
            addCriterion("fuxi_bot_id >=", value, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdLessThan(Long value) {
            addCriterion("fuxi_bot_id <", value, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdLessThanOrEqualTo(Long value) {
            addCriterion("fuxi_bot_id <=", value, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdIn(List<Long> values) {
            addCriterion("fuxi_bot_id in", values, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdNotIn(List<Long> values) {
            addCriterion("fuxi_bot_id not in", values, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdBetween(Long value1, Long value2) {
            addCriterion("fuxi_bot_id between", value1, value2, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotIdNotBetween(Long value1, Long value2) {
            addCriterion("fuxi_bot_id not between", value1, value2, "fuxiBotId");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionIsNull() {
            addCriterion("fuxi_bot_version is null");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionIsNotNull() {
            addCriterion("fuxi_bot_version is not null");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionEqualTo(Integer value) {
            addCriterion("fuxi_bot_version =", value, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionNotEqualTo(Integer value) {
            addCriterion("fuxi_bot_version <>", value, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionGreaterThan(Integer value) {
            addCriterion("fuxi_bot_version >", value, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("fuxi_bot_version >=", value, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionLessThan(Integer value) {
            addCriterion("fuxi_bot_version <", value, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionLessThanOrEqualTo(Integer value) {
            addCriterion("fuxi_bot_version <=", value, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionIn(List<Integer> values) {
            addCriterion("fuxi_bot_version in", values, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionNotIn(List<Integer> values) {
            addCriterion("fuxi_bot_version not in", values, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionBetween(Integer value1, Integer value2) {
            addCriterion("fuxi_bot_version between", value1, value2, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andFuxiBotVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("fuxi_bot_version not between", value1, value2, "fuxiBotVersion");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeIsNull() {
            addCriterion("distribute_type is null");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeIsNotNull() {
            addCriterion("distribute_type is not null");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeEqualTo(Integer value) {
            addCriterion("distribute_type =", value, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeNotEqualTo(Integer value) {
            addCriterion("distribute_type <>", value, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeGreaterThan(Integer value) {
            addCriterion("distribute_type >", value, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("distribute_type >=", value, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeLessThan(Integer value) {
            addCriterion("distribute_type <", value, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("distribute_type <=", value, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeIn(List<Integer> values) {
            addCriterion("distribute_type in", values, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeNotIn(List<Integer> values) {
            addCriterion("distribute_type not in", values, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeBetween(Integer value1, Integer value2) {
            addCriterion("distribute_type between", value1, value2, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDistributeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("distribute_type not between", value1, value2, "distributeType");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdIsNull() {
            addCriterion("dxbot_app_id is null");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdIsNotNull() {
            addCriterion("dxbot_app_id is not null");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdEqualTo(String value) {
            addCriterion("dxbot_app_id =", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdNotEqualTo(String value) {
            addCriterion("dxbot_app_id <>", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdGreaterThan(String value) {
            addCriterion("dxbot_app_id >", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("dxbot_app_id >=", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdLessThan(String value) {
            addCriterion("dxbot_app_id <", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdLessThanOrEqualTo(String value) {
            addCriterion("dxbot_app_id <=", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdLike(String value) {
            addCriterion("dxbot_app_id like", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdNotLike(String value) {
            addCriterion("dxbot_app_id not like", value, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdIn(List<String> values) {
            addCriterion("dxbot_app_id in", values, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdNotIn(List<String> values) {
            addCriterion("dxbot_app_id not in", values, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdBetween(String value1, String value2) {
            addCriterion("dxbot_app_id between", value1, value2, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppIdNotBetween(String value1, String value2) {
            addCriterion("dxbot_app_id not between", value1, value2, "dxbotAppId");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretIsNull() {
            addCriterion("dxbot_app_secret is null");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretIsNotNull() {
            addCriterion("dxbot_app_secret is not null");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretEqualTo(String value) {
            addCriterion("dxbot_app_secret =", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretNotEqualTo(String value) {
            addCriterion("dxbot_app_secret <>", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretGreaterThan(String value) {
            addCriterion("dxbot_app_secret >", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretGreaterThanOrEqualTo(String value) {
            addCriterion("dxbot_app_secret >=", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretLessThan(String value) {
            addCriterion("dxbot_app_secret <", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretLessThanOrEqualTo(String value) {
            addCriterion("dxbot_app_secret <=", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretLike(String value) {
            addCriterion("dxbot_app_secret like", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretNotLike(String value) {
            addCriterion("dxbot_app_secret not like", value, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretIn(List<String> values) {
            addCriterion("dxbot_app_secret in", values, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretNotIn(List<String> values) {
            addCriterion("dxbot_app_secret not in", values, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretBetween(String value1, String value2) {
            addCriterion("dxbot_app_secret between", value1, value2, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotAppSecretNotBetween(String value1, String value2) {
            addCriterion("dxbot_app_secret not between", value1, value2, "dxbotAppSecret");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdIsNull() {
            addCriterion("dxbot_bot_id is null");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdIsNotNull() {
            addCriterion("dxbot_bot_id is not null");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdEqualTo(Long value) {
            addCriterion("dxbot_bot_id =", value, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdNotEqualTo(Long value) {
            addCriterion("dxbot_bot_id <>", value, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdGreaterThan(Long value) {
            addCriterion("dxbot_bot_id >", value, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdGreaterThanOrEqualTo(Long value) {
            addCriterion("dxbot_bot_id >=", value, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdLessThan(Long value) {
            addCriterion("dxbot_bot_id <", value, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdLessThanOrEqualTo(Long value) {
            addCriterion("dxbot_bot_id <=", value, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdIn(List<Long> values) {
            addCriterion("dxbot_bot_id in", values, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdNotIn(List<Long> values) {
            addCriterion("dxbot_bot_id not in", values, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdBetween(Long value1, Long value2) {
            addCriterion("dxbot_bot_id between", value1, value2, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotBotIdNotBetween(Long value1, Long value2) {
            addCriterion("dxbot_bot_id not between", value1, value2, "dxbotBotId");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeIsNull() {
            addCriterion("dxbot_run_mode is null");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeIsNotNull() {
            addCriterion("dxbot_run_mode is not null");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeEqualTo(Integer value) {
            addCriterion("dxbot_run_mode =", value, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeNotEqualTo(Integer value) {
            addCriterion("dxbot_run_mode <>", value, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeGreaterThan(Integer value) {
            addCriterion("dxbot_run_mode >", value, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("dxbot_run_mode >=", value, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeLessThan(Integer value) {
            addCriterion("dxbot_run_mode <", value, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeLessThanOrEqualTo(Integer value) {
            addCriterion("dxbot_run_mode <=", value, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeIn(List<Integer> values) {
            addCriterion("dxbot_run_mode in", values, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeNotIn(List<Integer> values) {
            addCriterion("dxbot_run_mode not in", values, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeBetween(Integer value1, Integer value2) {
            addCriterion("dxbot_run_mode between", value1, value2, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxbotRunModeNotBetween(Integer value1, Integer value2) {
            addCriterion("dxbot_run_mode not between", value1, value2, "dxbotRunMode");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdIsNull() {
            addCriterion("dxpub_pub_id is null");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdIsNotNull() {
            addCriterion("dxpub_pub_id is not null");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdEqualTo(Long value) {
            addCriterion("dxpub_pub_id =", value, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdNotEqualTo(Long value) {
            addCriterion("dxpub_pub_id <>", value, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdGreaterThan(Long value) {
            addCriterion("dxpub_pub_id >", value, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdGreaterThanOrEqualTo(Long value) {
            addCriterion("dxpub_pub_id >=", value, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdLessThan(Long value) {
            addCriterion("dxpub_pub_id <", value, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdLessThanOrEqualTo(Long value) {
            addCriterion("dxpub_pub_id <=", value, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdIn(List<Long> values) {
            addCriterion("dxpub_pub_id in", values, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdNotIn(List<Long> values) {
            addCriterion("dxpub_pub_id not in", values, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdBetween(Long value1, Long value2) {
            addCriterion("dxpub_pub_id between", value1, value2, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubPubIdNotBetween(Long value1, Long value2) {
            addCriterion("dxpub_pub_id not between", value1, value2, "dxpubPubId");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyIsNull() {
            addCriterion("dxpub_key is null");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyIsNotNull() {
            addCriterion("dxpub_key is not null");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyEqualTo(String value) {
            addCriterion("dxpub_key =", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyNotEqualTo(String value) {
            addCriterion("dxpub_key <>", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyGreaterThan(String value) {
            addCriterion("dxpub_key >", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyGreaterThanOrEqualTo(String value) {
            addCriterion("dxpub_key >=", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyLessThan(String value) {
            addCriterion("dxpub_key <", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyLessThanOrEqualTo(String value) {
            addCriterion("dxpub_key <=", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyLike(String value) {
            addCriterion("dxpub_key like", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyNotLike(String value) {
            addCriterion("dxpub_key not like", value, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyIn(List<String> values) {
            addCriterion("dxpub_key in", values, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyNotIn(List<String> values) {
            addCriterion("dxpub_key not in", values, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyBetween(String value1, String value2) {
            addCriterion("dxpub_key between", value1, value2, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubKeyNotBetween(String value1, String value2) {
            addCriterion("dxpub_key not between", value1, value2, "dxpubKey");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenIsNull() {
            addCriterion("dxpub_token is null");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenIsNotNull() {
            addCriterion("dxpub_token is not null");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenEqualTo(String value) {
            addCriterion("dxpub_token =", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenNotEqualTo(String value) {
            addCriterion("dxpub_token <>", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenGreaterThan(String value) {
            addCriterion("dxpub_token >", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenGreaterThanOrEqualTo(String value) {
            addCriterion("dxpub_token >=", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenLessThan(String value) {
            addCriterion("dxpub_token <", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenLessThanOrEqualTo(String value) {
            addCriterion("dxpub_token <=", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenLike(String value) {
            addCriterion("dxpub_token like", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenNotLike(String value) {
            addCriterion("dxpub_token not like", value, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenIn(List<String> values) {
            addCriterion("dxpub_token in", values, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenNotIn(List<String> values) {
            addCriterion("dxpub_token not in", values, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenBetween(String value1, String value2) {
            addCriterion("dxpub_token between", value1, value2, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andDxpubTokenNotBetween(String value1, String value2) {
            addCriterion("dxpub_token not between", value1, value2, "dxpubToken");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdIsNull() {
            addCriterion("wh_muxing_tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdIsNotNull() {
            addCriterion("wh_muxing_tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdEqualTo(String value) {
            addCriterion("wh_muxing_tenant_id =", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdNotEqualTo(String value) {
            addCriterion("wh_muxing_tenant_id <>", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdGreaterThan(String value) {
            addCriterion("wh_muxing_tenant_id >", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("wh_muxing_tenant_id >=", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdLessThan(String value) {
            addCriterion("wh_muxing_tenant_id <", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdLessThanOrEqualTo(String value) {
            addCriterion("wh_muxing_tenant_id <=", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdLike(String value) {
            addCriterion("wh_muxing_tenant_id like", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdNotLike(String value) {
            addCriterion("wh_muxing_tenant_id not like", value, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdIn(List<String> values) {
            addCriterion("wh_muxing_tenant_id in", values, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdNotIn(List<String> values) {
            addCriterion("wh_muxing_tenant_id not in", values, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdBetween(String value1, String value2) {
            addCriterion("wh_muxing_tenant_id between", value1, value2, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingTenantIdNotBetween(String value1, String value2) {
            addCriterion("wh_muxing_tenant_id not between", value1, value2, "whMuxingTenantId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdIsNull() {
            addCriterion("wh_muxing_route_id is null");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdIsNotNull() {
            addCriterion("wh_muxing_route_id is not null");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdEqualTo(String value) {
            addCriterion("wh_muxing_route_id =", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdNotEqualTo(String value) {
            addCriterion("wh_muxing_route_id <>", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdGreaterThan(String value) {
            addCriterion("wh_muxing_route_id >", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdGreaterThanOrEqualTo(String value) {
            addCriterion("wh_muxing_route_id >=", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdLessThan(String value) {
            addCriterion("wh_muxing_route_id <", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdLessThanOrEqualTo(String value) {
            addCriterion("wh_muxing_route_id <=", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdLike(String value) {
            addCriterion("wh_muxing_route_id like", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdNotLike(String value) {
            addCriterion("wh_muxing_route_id not like", value, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdIn(List<String> values) {
            addCriterion("wh_muxing_route_id in", values, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdNotIn(List<String> values) {
            addCriterion("wh_muxing_route_id not in", values, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdBetween(String value1, String value2) {
            addCriterion("wh_muxing_route_id between", value1, value2, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhMuxingRouteIdNotBetween(String value1, String value2) {
            addCriterion("wh_muxing_route_id not between", value1, value2, "whMuxingRouteId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdIsNull() {
            addCriterion("wh_xm_bot_id is null");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdIsNotNull() {
            addCriterion("wh_xm_bot_id is not null");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdEqualTo(String value) {
            addCriterion("wh_xm_bot_id =", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdNotEqualTo(String value) {
            addCriterion("wh_xm_bot_id <>", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdGreaterThan(String value) {
            addCriterion("wh_xm_bot_id >", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdGreaterThanOrEqualTo(String value) {
            addCriterion("wh_xm_bot_id >=", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdLessThan(String value) {
            addCriterion("wh_xm_bot_id <", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdLessThanOrEqualTo(String value) {
            addCriterion("wh_xm_bot_id <=", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdLike(String value) {
            addCriterion("wh_xm_bot_id like", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdNotLike(String value) {
            addCriterion("wh_xm_bot_id not like", value, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdIn(List<String> values) {
            addCriterion("wh_xm_bot_id in", values, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdNotIn(List<String> values) {
            addCriterion("wh_xm_bot_id not in", values, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdBetween(String value1, String value2) {
            addCriterion("wh_xm_bot_id between", value1, value2, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhXmBotIdNotBetween(String value1, String value2) {
            addCriterion("wh_xm_bot_id not between", value1, value2, "whXmBotId");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigIsNull() {
            addCriterion("wh_tts_config is null");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigIsNotNull() {
            addCriterion("wh_tts_config is not null");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigEqualTo(String value) {
            addCriterion("wh_tts_config =", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigNotEqualTo(String value) {
            addCriterion("wh_tts_config <>", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigGreaterThan(String value) {
            addCriterion("wh_tts_config >", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigGreaterThanOrEqualTo(String value) {
            addCriterion("wh_tts_config >=", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigLessThan(String value) {
            addCriterion("wh_tts_config <", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigLessThanOrEqualTo(String value) {
            addCriterion("wh_tts_config <=", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigLike(String value) {
            addCriterion("wh_tts_config like", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigNotLike(String value) {
            addCriterion("wh_tts_config not like", value, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigIn(List<String> values) {
            addCriterion("wh_tts_config in", values, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigNotIn(List<String> values) {
            addCriterion("wh_tts_config not in", values, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigBetween(String value1, String value2) {
            addCriterion("wh_tts_config between", value1, value2, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andWhTtsConfigNotBetween(String value1, String value2) {
            addCriterion("wh_tts_config not between", value1, value2, "whTtsConfig");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdIsNull() {
            addCriterion("chat_history_flow_id is null");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdIsNotNull() {
            addCriterion("chat_history_flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdEqualTo(Long value) {
            addCriterion("chat_history_flow_id =", value, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdNotEqualTo(Long value) {
            addCriterion("chat_history_flow_id <>", value, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdGreaterThan(Long value) {
            addCriterion("chat_history_flow_id >", value, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdGreaterThanOrEqualTo(Long value) {
            addCriterion("chat_history_flow_id >=", value, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdLessThan(Long value) {
            addCriterion("chat_history_flow_id <", value, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdLessThanOrEqualTo(Long value) {
            addCriterion("chat_history_flow_id <=", value, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdIn(List<Long> values) {
            addCriterion("chat_history_flow_id in", values, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdNotIn(List<Long> values) {
            addCriterion("chat_history_flow_id not in", values, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdBetween(Long value1, Long value2) {
            addCriterion("chat_history_flow_id between", value1, value2, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andChatHistoryFlowIdNotBetween(Long value1, Long value2) {
            addCriterion("chat_history_flow_id not between", value1, value2, "chatHistoryFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdIsNull() {
            addCriterion("wh_param_handle_flow_id is null");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdIsNotNull() {
            addCriterion("wh_param_handle_flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdEqualTo(Long value) {
            addCriterion("wh_param_handle_flow_id =", value, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdNotEqualTo(Long value) {
            addCriterion("wh_param_handle_flow_id <>", value, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdGreaterThan(Long value) {
            addCriterion("wh_param_handle_flow_id >", value, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdGreaterThanOrEqualTo(Long value) {
            addCriterion("wh_param_handle_flow_id >=", value, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdLessThan(Long value) {
            addCriterion("wh_param_handle_flow_id <", value, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdLessThanOrEqualTo(Long value) {
            addCriterion("wh_param_handle_flow_id <=", value, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdIn(List<Long> values) {
            addCriterion("wh_param_handle_flow_id in", values, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdNotIn(List<Long> values) {
            addCriterion("wh_param_handle_flow_id not in", values, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdBetween(Long value1, Long value2) {
            addCriterion("wh_param_handle_flow_id between", value1, value2, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andWhParamHandleFlowIdNotBetween(Long value1, Long value2) {
            addCriterion("wh_param_handle_flow_id not between", value1, value2, "whParamHandleFlowId");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andEditorIsNull() {
            addCriterion("editor is null");
            return (Criteria) this;
        }

        public Criteria andEditorIsNotNull() {
            addCriterion("editor is not null");
            return (Criteria) this;
        }

        public Criteria andEditorEqualTo(String value) {
            addCriterion("editor =", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotEqualTo(String value) {
            addCriterion("editor <>", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorGreaterThan(String value) {
            addCriterion("editor >", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorGreaterThanOrEqualTo(String value) {
            addCriterion("editor >=", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLessThan(String value) {
            addCriterion("editor <", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLessThanOrEqualTo(String value) {
            addCriterion("editor <=", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLike(String value) {
            addCriterion("editor like", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotLike(String value) {
            addCriterion("editor not like", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorIn(List<String> values) {
            addCriterion("editor in", values, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotIn(List<String> values) {
            addCriterion("editor not in", values, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorBetween(String value1, String value2) {
            addCriterion("editor between", value1, value2, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotBetween(String value1, String value2) {
            addCriterion("editor not between", value1, value2, "editor");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}