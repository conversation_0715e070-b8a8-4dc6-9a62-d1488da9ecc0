package com.sankuai.gaigc.arrange.dao.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: ai_bot_enhance
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AIBotEnhanceDO {
    /**
     *   字段: id
     *   说明: 功能ID
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 功能名称
     */
    private String name;

    /**
     *   字段: class_name
     *   说明: 配置类名
     */
    private String className;

    /**
     *   字段: icon
     *   说明: icon
     */
    private String icon;

    /**
     *   字段: description
     *   说明: 功能描述
     */
    private String description;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 修改时间
     */
    private Date updateTime;

    /**
     *   字段: default_config
     *   说明: 默认配置
     */
    private String defaultConfig;
}