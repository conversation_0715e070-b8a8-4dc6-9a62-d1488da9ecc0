<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.gaigc.arrange.dao.dal.mapper.KnowledgeSourceMapper">

    <!--auto generated Code-->
    <resultMap id="AllColumnMap" type="com.sankuai.gaigc.arrange.dao.dal.entity.KnowledgeSourceDO">
        <result column="id" property="id"/>
        <result column="knowledge_base_id" property="knowledgeBaseId"/>
        <result column="name" property="name"/>
        <result column="knowledge_source" property="knowledgeSource"/>
        <result column="meta_info" property="metaInfo"/>
        <result column="source_config" property="sourceConfig"/>
        <result column="config_class" property="configClass"/>
        <result column="status" property="status"/>
        <result column="execute_info" property="executeInfo"/>
        <result column="creator" property="creator"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!--auto generated Code-->
    <sql id="all_column">
        id
        ,
        knowledge_base_id,
        name,
        knowledge_source,
        meta_info,
        source_config,
        config_class,
        status,
        execute_info,
        creator,
        create_time,
        update_time
    </sql>

    <!--auto generated Code-->
    <sql id="all_column_without_id">
        knowledge_base_id
        ,
        name,
        knowledge_source,
        meta_info,
        source_config,
        config_class,
        status,
        execute_info,
        creator,
        create_time,
        update_time
    </sql>

    <select id="selectByIds" resultMap="AllColumnMap">
        SELECT
        <include refid="all_column"/>
        FROM knowledge_source
        WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectByKnowledgeBaseId" resultMap="AllColumnMap">
        SELECT
        <include refid="all_column"/>
        FROM knowledge_source
        WHERE knowledge_base_id = #{knowledgeBaseId}
    </select>

</mapper>
