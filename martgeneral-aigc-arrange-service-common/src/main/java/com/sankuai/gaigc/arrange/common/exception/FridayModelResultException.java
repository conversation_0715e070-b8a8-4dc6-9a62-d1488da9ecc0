/*
 * Copyright (c) 2022 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.exception;

/**
 * 模型结果异常
 *
 * <AUTHOR>
 * @created 2022/3/3
 */
public class FridayModelResultException extends RuntimeException {
    public FridayModelResultException() {
        super();
    }

    public FridayModelResultException(String message) {
        super(message);
    }

    public FridayModelResultException(Throwable cause) {
        super(cause);
    }

    public FridayModelResultException(String message, Throwable cause) {
        super(message, cause);
    }
}