package com.sankuai.gaigc.arrange.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * date 2019/12/26
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ApplicationContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationContextUtil.applicationContext = applicationContext;
    }

    public static <T> T getBean(Class<T> claz){
        return ApplicationContextUtil.applicationContext.getBean(claz);
    }

    public static Object getBeanByName(String name){
        return ApplicationContextUtil.applicationContext.getBean(name);
    }

    public static <T>  Map<String, T>  getBeanByType(Class<T> claz){
        return ApplicationContextUtil.applicationContext.getBeansOfType(claz);
    }

    public static String getPropertiesValue(String key){
        return ApplicationContextUtil.applicationContext.getEnvironment().getProperty(key);
    }
}