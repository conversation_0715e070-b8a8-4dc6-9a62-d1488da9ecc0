/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.constant;

import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.solarsystem.aigc.basicapi.global.common.util.ThreadPoolUtils;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * ThreadPoolConstants
 *
 * <AUTHOR>
 * @created 2024/7/2
 */
public interface ThreadPoolConstants {
    /**
     * 多计划运行模板线程池
     */
    ThreadPool MULTI_PLAN_BOT_TEMPLATE_POOL = ThreadPoolUtils.getInstance4Io("AbstractMultiPlanAgent.run", new ThreadPoolExecutor.CallerRunsPolicy());

    ThreadPool BOT_ENHANCE_POOL = ThreadPoolUtils.getInstance4Io("BotStartEnhanceHandler.run", new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 批量路线规划线程池
     */
    ThreadPool BATCH_ROUTE_GUIDE_POOL = ThreadPoolUtils.getDefaultInstance("BatchRouteGuide.execute");

    /**
     * 输入提示线程池
     */
    ThreadPool BATCH_INPUT_SUGGEST_POOL = ThreadPoolUtils.getDefaultInstance("BatchInputSuggest.execute");
}
