package com.sankuai.gaigc.arrange.common.constant;

/**
 * <AUTHOR>
 * @date 2024/4/24 14:51
 */
public class MessageTypeConstants {
    //表示接收到了用户发起的消息
    public static final String ACK = "ack";
    //大模型生成执行计划
    public static final String PLAN = "plan";
    //过程情况信息
    public static final String PROCESS = "process";
    //函数调用的入参
    public static final String FUNCTION_CALL = "function_call";
    //函数调用的结果
    public static final String FUNCTION_RESPONSE = "function_response";
    //查询知识库的结果
    public static final String KNOWLEDGE = "knowledge";
    //大模型回复的结果
    public static final String ANSWER = "answer";

    public static final String FLOW_RESULT = "flow_result";
    public static final String BOT_RESULT = "bot_result";
    public static final String CARD_REPLY = "card_reply";
    //用户可追问的问题提示
    public static final String FOLLOW_UP = "follow_up";
    /** 引用和归属 */
    public static final String ANSWER_REFERENCE = "answer_reference";
    /** 相关推荐 */
    public static final String QUERY_RECOMMEND = "query_recommend";
    /** 扩展信息 */
    public static final String EXPAND_INFO = "expand_info";
}
