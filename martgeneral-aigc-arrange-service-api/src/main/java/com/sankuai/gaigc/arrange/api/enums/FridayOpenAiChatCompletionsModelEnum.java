package com.sankuai.gaigc.arrange.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Friday - One-API 大模型统一接口model枚举
 * 模型参考:
 *
 * @see <a href="https://km.sankuai.com/collabpage/1580139661">FRIDAY大模型平台文档</a>
 * 张俊杰 2024年05月22日16:53:19
 */
@AllArgsConstructor
@Getter
public enum FridayOpenAiChatCompletionsModelEnum {
    /**
     * 支持json_object
     */
    LongCat_Prime_8K_Chat("LongCat-Prime-8K-Chat"),
    /**
     * 支持json_object
     */
    LongCat_Large_4K_Chat("LongCat-Large-4K-Chat"),
    /**
     * 不支持json_object
     */
    gpt_4_0613("gpt-4-0613"),
    /**
     * 支持json_object
     */
    gpt_4_turbo_2024_04_09("gpt-4-turbo-2024-04-09"),
    /**
     * 支持json_object
     */
    gpt_4o_2024_05_13("gpt-4o-2024-05-13"),
    /**
     * 不支持json_object
     */
    gpt_3_5_turbo_0613("gpt-3.5-turbo-0613"),
    /**
     * 支持json_object
     */
    gpt_3_5_turbo_1106("gpt-3.5-turbo-1106"),
    /**
     * 不支持json_object
     */
    gpt_3_5_turbo_16k("gpt-3.5-turbo-16k");

    private final String modelName;
}
