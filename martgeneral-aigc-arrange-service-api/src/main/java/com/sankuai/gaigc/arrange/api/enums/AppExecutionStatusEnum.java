package com.sankuai.gaigc.arrange.api.enums;

/**
 * @Author: lin<PERSON>quan
 * @Description: 应用执行状态
 * @Date: 2024-08-22
 */

public enum AppExecutionStatusEnum {
    /**
     * 成功
     */
    SUCCESS(1),
    /**
     * 异常
     */
    EXCEPTION(2);

    private final Integer type;

    AppExecutionStatusEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public static AppExecutionStatusEnum findByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (AppExecutionStatusEnum modeEnum : AppExecutionStatusEnum.values()) {
            if (modeEnum.getType().equals(type)) {
                return modeEnum;
            }
        }
        return null;
    }
}
