package com.sankuai.gaigc.arrange.api.entity.bot;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@ThriftStruct
@Getter
@Setter
public class BotRunResponse {
    /**
     * 状态码：200 执行成功；错误码：ErrorCodeEnum
     * */
    @ThriftField(value = 1)
    private Integer code;

    /**
     * 错误信息描述，code非200时有效
     * */
    @ThriftField(value = 2)
    private String msg;

    /**
     * Bot执行结果
     * */
    @ThriftField(value = 3)
    private BotExecuteResult data;

    /**
     * traceId
     * */
    @ThriftField(value = 4)
    private String traceId;

    /**
     * 是否执行成功
     * */
    public boolean isSuccess() {
        return Objects.nonNull(code) && code.equals(200);
    }
}
