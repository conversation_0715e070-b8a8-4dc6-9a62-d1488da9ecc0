package com.sankuai.gaigc.arrange.api.enums;

import com.dianping.cat.util.StringUtils;

/**
 * @Author: linboquan
 * @Description: 伏羲调用流量来源枚举
 * @Date: 2024-08-22
 */

public enum FuxiCallSourceEnum {
    /**
     * "页面调用"
     */
    FUXI_PAGE(1,"FUXI_PAGE"),
    /**
     * "API调用"
     */
    API(2,"API"),
    /**
     * "QA"
     */
    QA(3,"QA");

    private final Integer type;
    private final String name;

    FuxiCallSourceEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }
    public String getName() {
        return name;
    }

    public static FuxiCallSourceEnum findByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (FuxiCallSourceEnum modeEnum : FuxiCallSourceEnum.values()) {
            if (modeEnum.getType().equals(type)) {
                return modeEnum;
            }
        }
        return null;
    }
    public static FuxiCallSourceEnum findByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (FuxiCallSourceEnum modeEnum : FuxiCallSourceEnum.values()) {
            if (modeEnum.getName().equals(name)) {
                return modeEnum;
            }
        }
        return null;
    }

}
