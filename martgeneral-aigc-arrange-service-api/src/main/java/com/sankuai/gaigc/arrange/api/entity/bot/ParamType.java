package com.sankuai.gaigc.arrange.api.entity.bot;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

@ThriftEnum
public enum ParamType {
    BOOLEAN(0),
    BYTE(1),
    SHORT(2),
    INTEGER(3),
    LONG(4),
    FLOAT(5),
    DOUBLE(6),
    CHARACTER(7),
    STRING(8),
    LIST(9),
    MAP(10),
    SET(11),
    OBJECT(12);

    private final int value;

    ParamType(int value) {
        this.value = value;
    }

    @ThriftEnumValue
    public int getValue() {
        return value;
    }

    public static ParamType findByValue(int value) {
        switch (value) {
            case 0:
                return BOOLEAN;
            case 1:
                return BYTE;
            case 2:
                return SHORT;
            case 3:
                return INTEGER;
            case 4:
                return LONG;
            case 5:
                return FLOAT;
            case 6:
                return DOUBLE;
            case 7:
                return CHARACTER;
            case 8:
                return STRING;
            case 9:
                return LIST;
            case 10:
                return MAP;
            case 11:
                return SET;
            case 12:
                return OBJECT;
            default:
                return null;
        }
    }
}

