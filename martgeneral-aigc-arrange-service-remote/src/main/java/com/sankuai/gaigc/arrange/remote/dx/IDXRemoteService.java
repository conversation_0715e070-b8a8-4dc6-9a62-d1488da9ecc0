package com.sankuai.gaigc.arrange.remote.dx;



import com.sankuai.gaigc.arrange.remote.dx.vo.DXChatRequest;
import com.sankuai.gaigc.arrange.remote.dx.vo.DXChatResponse;

import java.util.Set;

/**
 * 大象服务
 *
 * <AUTHOR>
 * @date 2024/9/16 07:16
 * @desc
 */
public interface IDXRemoteService {
    /**-------------用户配置机器人消息发送方法-----------*/
    DXChatResponse sendChatMsgByUserRobot(DXChatRequest req, Set<Long> receiverIds, String dxAppkey, String dxAppSecret);

    DXChatResponse sendGroupMsgByUserRobot(DXChatRequest req, String groupId, String dxAppkey, String dxAppSecret);
}
