package com.sankuai.gaigc.arrange.remote.dx.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.gaigc.arrange.remote.dx.IDXRemoteService;
import com.sankuai.gaigc.arrange.remote.dx.vo.DXChatRequest;
import com.sankuai.gaigc.arrange.remote.dx.vo.DXChatResponse;
import com.sankuai.xm.openplatform.api.entity.*;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.openplatform.auth.entity.AccessToken;
import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 大象服务
 *
 * <AUTHOR>
 * @date 2024/9/6
 */
@Slf4j
@Service
public class DXRemoteServiceImpl implements IDXRemoteService {
    private final static int EXPIRE_ONE_HOUR = 60 * 60;
    /** 大象机器人访问令牌缓存 */
    public static final String CATEGORY_DX_ROBOT_ACCESS_TOKEN = "dx_robot_access_token";

    @MdpThriftClient(remoteAppKey = "com.sankuai.dxenterprise.open.gateway", timeout = 1000)
    private XmOpenMessageServiceI.Iface xmOpenMsgService;
    @MdpThriftClient(remoteAppKey = "com.sankuai.dxenterprise.open.gateway", timeout = 1000)
    private XmAuthServiceI.Iface dxXmAuthService;

    @Resource(name = "redisClient0")
    private RedisStoreClient aigcRedisStoreClient;

    //-------------用户配置大象机器人消息发送-----------
    @Override
    public DXChatResponse sendChatMsgByUserRobot(DXChatRequest req, Set<Long> receiverIds, String dxAppkey, String dxAppSecret) {
        try {
            String accessToken = getToken4DxRobot(dxAppkey, dxAppSecret);
            return sendDxMsgByBot(req, receiverIds, accessToken);
        } catch (Exception e) {
            log.error("[IDXRemoteService] 发送大象消息失败", e);
            return null;
        }
    }

    public DXChatResponse sendGroupMsgByUserRobot(DXChatRequest req, String groupId, String dxAppkey, String dxAppSecret) {
        try {
            String accessToken = getToken4DxRobot(dxAppkey, dxAppSecret);
            return sendDxGroupMsgByBot(req, groupId, accessToken);
        } catch (Exception e) {
            log.error("[IDXRemoteService] 发送大象消息失败", e);
            return null;
        }
    }

    //-------------用户配置大象机器人消息发送-----------


    /**
     * 通过大象机器人发送大象单聊消息
     *
     * @param req DXChatRequest 请求对象，包含消息内容等信息
     * @param receiverIds 接收者ID集合
     * @param accessToken 访问令牌
     */
    @Nullable
    private DXChatResponse sendDxMsgByBot(DXChatRequest req, Set<Long> receiverIds, String accessToken) throws TException {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(receiverIds),"接收人ID不可为空");

        SendChatMsgByRobotRes sendChatMsgByRobotRes = xmOpenMsgService.sendChatMsgByRobot(accessToken,
                buildRobotChatReq(req, receiverIds));

        return DXChatResponse.valueOf(sendChatMsgByRobotRes);
    }

    /**
     * 通过大象机器人发送群聊消息
     */
    private DXChatResponse sendDxGroupMsgByBot(DXChatRequest req, String groupId, String accessToken) throws TException {
        Preconditions.checkArgument(StringUtils.isNotBlank(groupId),"群ID不可为空");

        SendGroupMsgByRobotRes sendGroupMsgByRobotRes = xmOpenMsgService.sendGroupMsgByRobot(accessToken,
                buildRobotGroupReq(req, groupId));

        return DXChatResponse.valueOf(sendGroupMsgByRobotRes);
    }
    
    private String getToken4DxRobot(String dxAppkey, String dxAppSecret) throws TException {
        //接口有限频 先查缓存
        StoreKey dxRobotToken = new StoreKey(CATEGORY_DX_ROBOT_ACCESS_TOKEN, dxAppkey);
        if (aigcRedisStoreClient.exists(dxRobotToken)) {
            return aigcRedisStoreClient.get(dxRobotToken);
        }

        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(dxAppkey);
        appAuthInfo.setAppSecret(dxAppSecret);
        AccessToken accessToken = getDxAccessToken(appAuthInfo);
        //拿到的token可能是个快过期的
        Long expireTime = accessToken.getExpireTime();
        Long currentTime = System.currentTimeMillis();
        long expireInSeconds = (expireTime - currentTime) / 1000;

        //写缓存
        aigcRedisStoreClient.set(dxRobotToken, accessToken.getToken(), Math.toIntExact(expireInSeconds));
        return accessToken.getToken();
    }

    /**
     * 获取大象平台的访问令牌
     *
     * @param appAuthInfo 大象应用认证信息
     * @return 访问令牌字符串
     */
    private AccessToken getDxAccessToken(AppAuthInfo appAuthInfo) throws TException {
        AccessTokenResp resp = dxXmAuthService.accessToken(appAuthInfo);
        if(resp.status.getCode()==0) {
            return resp.getAccessToken();
        }
        log.error("生成开放平台Token失败,{}{}",resp.getStatus().getCode(), resp.getStatus().getMsg());
        throw new RuntimeException(resp.getStatus().getMsg());
    }

    //包装为单聊消息发送格式
    private SendChatMsgByRobotReq buildRobotChatReq(DXChatRequest req, Set<Long> receiverIds) {
        SendChatMsgByRobotReq sendChatMsgByRobotReq = new SendChatMsgByRobotReq();
        sendChatMsgByRobotReq.setReceiverIds(Lists.newArrayList(receiverIds));
        sendChatMsgByRobotReq.setSendMsgInfo(getSendMsgInfo(req));
        return sendChatMsgByRobotReq;
    }

    //包装为群聊消息发送格式
    private SendGroupMsgByRobotReq buildRobotGroupReq(DXChatRequest req,String groupId) {
        SendGroupMsgByRobotReq sendChatMsgByRobotReq = new SendGroupMsgByRobotReq();
        sendChatMsgByRobotReq.setGid(Long.parseLong(groupId));
        sendChatMsgByRobotReq.setSendMsgInfo(getSendMsgInfo(req));
        return sendChatMsgByRobotReq;
    }

    @NotNull
    private static SendMsgInfo getSendMsgInfo(DXChatRequest req) {
        SendMsgInfo sendMsgInfo = new SendMsgInfo();
        sendMsgInfo.setType(req.getMsgType());
        sendMsgInfo.setBody(req.getBody());
        sendMsgInfo.setExtension(req.getExtension());
        sendMsgInfo.setIsDynamicMsg(false);
        return sendMsgInfo;
    }
}
