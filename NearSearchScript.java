package com.sankuai.aitool.runtime.script;

import com.sankuai.aitool.runtime.engine.annotation.Parameter;
import com.sankuai.aitool.runtime.engine.model.JavaEngineCode;
import com.sankuai.aitool.runtime.engine.model.Response;
import com.sankuai.aitool.runtime.engine.rpc.RpcProcessor;
import com.sankuai.aitool.runtime.engine.rpc.impl.RpcRrocessorFactory;
import com.sankuai.aitool.runtime.engine.script.AircraftScriptExcutor;
import com.sankuai.aitool.runtime.engine.util.JacksonUtil;
import com.sankuai.aitool.runtime.engine.util.json.FreeMarkerParser;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class NearSearchScript implements AircraftScriptExcutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AircraftScriptExcutor.class);
    private static final String LOG_PREFIX = "[AC_Java_NearSearch] ";

    private static final Integer NEAR_SEARCH_DISTANCE = 3000; // 搜索半径3000米

    @Override
    public void excutor(String paramJson, Response response) {
        try {
            LOGGER.info("{} start processing, paramJson = {}", LOG_PREFIX, paramJson);
            
            // 解析输入参数
            String location = FreeMarkerParser.parse(paramJson, "location");
            String keyWords = FreeMarkerParser.parse(paramJson, "keyWords");
            
            // 参数校验
            if (location == null || location.trim().isEmpty() || 
                keyWords == null || keyWords.trim().isEmpty()) {
                LOGGER.error("{} nearSearch执行失败,查询位置或关键词缺失, location={}, keyWords={}", 
                    LOG_PREFIX, location, keyWords);
                throw new IllegalArgumentException("查询位置和关键词都不能为null或空");
            }

            // 调用附近搜索服务
            List<Poi> pois = performNearSearch(location, keyWords);

            // 设置返回值
            OutputParam outputParam = new OutputParam();
            outputParam.setPois(pois);
            response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
            LOGGER.info("{} response = {}", LOG_PREFIX, JacksonUtil.toJsonStrWithEmptyDefault(response));
            
        } catch (IllegalArgumentException e) {
            LOGGER.error("{} parameter validation failed: {}", LOG_PREFIX, e.getMessage());
            response.setJavaEngineCode(JavaEngineCode.PARAMETER_ERROR);
        } catch (Exception e) {
            LOGGER.error("{} invoke failed e = {}", LOG_PREFIX, e);
            response.setJavaEngineCode(JavaEngineCode.SERVER_INNER_ERROR);
        }
    }

    private List<Poi> performNearSearch(String location, String keyWords) throws Exception {
        try {
            // 1.设置Thrift接口入参类型列表
            List<String> parameterType = new ArrayList<>();
            parameterType.add("mtmap.geoinfo.geoinfo_base.SearchServiceRequest");

            // 2.构造附近搜索请求参数
            Map<String, Object> searchRequest = new HashMap<>();
            searchRequest.put("location", location);
            searchRequest.put("keywords", keyWords);
            searchRequest.put("radius", NEAR_SEARCH_DISTANCE);
            // 注意：这里没有设置key，使用服务默认的key

            List<String> parameterArgs = new ArrayList<>();
            parameterArgs.add(JacksonUtils.simpleSerialize(searchRequest));

            // 3.获取Thrift接口实例
            RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                    "com.sankuai.map.open.platform.api.MapOpenApiService", 
                    "com.sankuai.apigw.map.facadecenter", 
                    5000, 
                    null, 
                    null);

            // 4.调用Thrift接口
            String jsonString = rpcProcessor.invoke("nearby", parameterType, parameterArgs);
            
            LOGGER.info("{} nearby search response = {}", LOG_PREFIX, jsonString);

            // 5.解析返回结果
            List<Poi> pois = new ArrayList<>();
            List<Map> poisData = FreeMarkerParser.convert2ListByPath(jsonString, "pois", Map.class);
            
            if (poisData != null && !poisData.isEmpty()) {
                for (Map poiData : poisData) {
                    Poi poi = convertToPoi(poiData);
                    if (poi != null) {
                        pois.add(poi);
                    }
                }
            }
            
            return pois;
            
        } catch (Exception e) {
            LOGGER.error("{} nearSearch执行失败, location:{}, keyWords:{}", LOG_PREFIX, location, keyWords, e);
            throw new RuntimeException("nearSearch执行失败", e);
        }
    }

    private Poi convertToPoi(Map poiData) {
        try {
            Poi poi = new Poi();
            
            // 基本信息
            Long poiId = FreeMarkerParser.parseLong(JacksonUtils.simpleSerialize(poiData), "poiId", 0L);
            poi.setPoiId(poiId);
            
            String poiName = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(poiData), "poiName");
            poi.setPoiName(poiName);
            
            String poiAddress = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(poiData), "poiAddress");
            poi.setPoiAddress(poiAddress);
            
            // 评分和价格信息
            Double avgScore = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(poiData), "avgScore", 0.0);
            poi.setAvgScore(avgScore);
            
            Integer avgPrice = FreeMarkerParser.parseInt(JacksonUtils.simpleSerialize(poiData), "avgPrice", 0);
            poi.setAvgPrice(avgPrice);
            
            Long commentCnt = FreeMarkerParser.parseLong(JacksonUtils.simpleSerialize(poiData), "poiCommentCnt", 0L);
            poi.setPoiCommentCnt(commentCnt);
            
            // 图片信息
            String frontImg = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(poiData), "frontImg");
            poi.setFrontImg(frontImg);
            
            // 跳转链接
            Map jumpUrlData = FreeMarkerParser.convert2ObjectByPath(JacksonUtils.simpleSerialize(poiData), "jumpUrl", Map.class);
            if (jumpUrlData != null) {
                JumpUrl jumpUrl = new JumpUrl();
                String url = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(jumpUrlData), "url");
                jumpUrl.setUrl(url);
                poi.setJumpUrl(jumpUrl);
            }
            
            // 位置信息
            Double latitude = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(poiData), "latitude", 0.0);
            poi.setLatitude(latitude);
            
            Double longitude = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(poiData), "longitude", 0.0);
            poi.setLongitude(longitude);
            
            // 其他信息
            String category = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(poiData), "category");
            poi.setCategory(category);
            
            String phone = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(poiData), "phone");
            poi.setPhone(phone);
            
            return poi;
            
        } catch (Exception e) {
            LOGGER.error("{} convertToPoi failed for poiData: {}", LOG_PREFIX, JacksonUtils.simpleSerialize(poiData), e);
            return null;
        }
    }

    @Data
    public static class InputParam {
        @Parameter(name="位置坐标")
        private String location;
        
        @Parameter(name="搜索关键词")
        private String keyWords;
    }

    @Data
    public static class OutputParam {
        @Parameter(name="POI列表")
        private List<Poi> pois;
    }
    
    @Data
    public static class Poi {
        @Parameter(name="POI ID")
        private Long poiId;
        
        @Parameter(name="POI名称")
        private String poiName;
        
        @Parameter(name="POI地址")
        private String poiAddress;
        
        @Parameter(name="平均评分")
        private Double avgScore;
        
        @Parameter(name="平均价格")
        private Integer avgPrice;
        
        @Parameter(name="评论数量")
        private Long poiCommentCnt;
        
        @Parameter(name="封面图片")
        private String frontImg;
        
        @Parameter(name="跳转链接")
        private JumpUrl jumpUrl;
        
        @Parameter(name="纬度")
        private Double latitude;
        
        @Parameter(name="经度")
        private Double longitude;
        
        @Parameter(name="分类")
        private String category;
        
        @Parameter(name="电话")
        private String phone;
    }
    
    @Data
    public static class JumpUrl {
        @Parameter(name="链接地址")
        private String url;
    }
}
