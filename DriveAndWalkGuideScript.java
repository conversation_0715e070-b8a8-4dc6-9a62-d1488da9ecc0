package com.sankuai.aitool.runtime.script;

import com.sankuai.aitool.runtime.engine.annotation.Parameter;
import com.sankuai.aitool.runtime.engine.model.JavaEngineCode;
import com.sankuai.aitool.runtime.engine.model.Response;
import com.sankuai.aitool.runtime.engine.rpc.RpcProcessor;
import com.sankuai.aitool.runtime.engine.rpc.impl.RpcRrocessorFactory;
import com.sankuai.aitool.runtime.engine.script.AircraftScriptExcutor;
import com.sankuai.aitool.runtime.engine.util.JacksonUtil;
import com.sankuai.aitool.runtime.engine.util.json.FreeMarkerParser;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class DriveAndWalkGuideScript implements AircraftScriptExcutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AircraftScriptExcutor.class);
    private static final String LOG_PREFIX = "[AC_Java_DriveAndWalkGuide] ";

    private static final String mapKeyTest = "me9236fd288c4eeda796224cd4ec91at";
    private static final String mapKeyProd = "m81618498c12405c854c545ce97ab12t";

    // 需要根据环境替换这个key！
    private static final String mapKey = mapKeyTest;

    private static final String DRIVING_TYPE = "driving";
    private static final String WALKING_TYPE = "walking";

    @Override
    public void excutor(String paramJson, Response response) {
        try {
            LOGGER.info("{} start processing, paramJson = {}", LOG_PREFIX, paramJson);

            // 解析输入参数
            String origin = FreeMarkerParser.parse(paramJson, "origin");
            String destination = FreeMarkerParser.parse(paramJson, "destination");

            // 参数校验
            if (origin == null || origin.trim().isEmpty() || destination == null || destination.trim().isEmpty()) {
                LOGGER.warn("{} origin or destination is blank", LOG_PREFIX);
                OutputParam outputParam = new OutputParam();
                outputParam.setRouteResults(new ArrayList<>());
                response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
                return;
            }

            List<RouteGuideResult> results = new ArrayList<>();

            // 获取驾车路线
            addDrivingRoute(results, origin, destination);

            // 获取步行路线
            addWalkingRoute(results, origin, destination);

            // 设置返回值
            OutputParam outputParam = new OutputParam();
            outputParam.setRouteResults(results);
            response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
            LOGGER.info("{} response = {}", LOG_PREFIX, JacksonUtil.toJsonStrWithEmptyDefault(response));

        } catch (Exception e) {
            LOGGER.error("{} invoke failed e = {}", LOG_PREFIX, e);
            response.setJavaEngineCode(JavaEngineCode.SERVER_INNER_ERROR);
        }
    }

    private void addDrivingRoute(List<RouteGuideResult> results, String origin, String destination) {
        try {
            // 1.设置Thrift接口入参类型列表
            List<String> parameterType = new ArrayList<>();
            parameterType.add("com.sankuai.map.open.platform.api.driving.DrivingRouteRequest");

            // 2.构造驾车路线请求参数
            Map<String, Object> drivingRequest = new HashMap<>();

            Map<String, Object> originMap = new HashMap<>();
            originMap.put("location", origin);
            drivingRequest.put("origin", originMap);

            Map<String, Object> destinationMap = new HashMap<>();
            destinationMap.put("location", destination);
            drivingRequest.put("destination", destinationMap);

            // 注意：这里没有传key，使用服务默认的key
            drivingRequest.put("show_fields", "duration");

            List<String> parameterArgs = new ArrayList<>();
            parameterArgs.add(JacksonUtils.simpleSerialize(drivingRequest));

            // 3.获取Thrift接口实例
            RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                    "com.sankuai.map.open.platform.api.MapOpenApiService",
                    "com.sankuai.apigw.map.facadecenter",
                    5000,
                    null,
                    null);

            // 4.调用Thrift接口
            String jsonString = rpcProcessor.invoke("driving", parameterType, parameterArgs);

            LOGGER.info("{} driving response = {}", LOG_PREFIX, jsonString);

            // 5.解析返回结果
            RouteGuideResult routeGuideResult = new RouteGuideResult();
            routeGuideResult.setType(DRIVING_TYPE);

            List<RouteResult> routeResults = new ArrayList<>();
            List<Map> routes = FreeMarkerParser.convert2ListByPath(jsonString, "route", Map.class);

            if (routes != null && !routes.isEmpty()) {
                for (Map route : routes) {
                    RouteResult routeResult = new RouteResult();

                    // 设置详细路线信息（非批量模式）
                    routeResult.setOrigin(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "origin"));
                    routeResult.setDestination(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "destination"));

                    Double distance = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(route), "distance", 0.0);
                    Integer duration = FreeMarkerParser.parseInt(JacksonUtils.simpleSerialize(route), "duration", 0);

                    routeResult.setDistance(distance);
                    routeResult.setDuration(duration.doubleValue());

                    routeResults.add(routeResult);
                }
            }

            routeGuideResult.setRouteResults(routeResults);
            results.add(routeGuideResult);

        } catch (Exception e) {
            LOGGER.error("{} addDrivingRoute failed", LOG_PREFIX, e);
            // 驾车路线获取失败不影响步行路线，继续执行
        }
    }

    private void addWalkingRoute(List<RouteGuideResult> results, String origin, String destination) {
        try {
            // 1.设置Thrift接口入参类型列表
            List<String> parameterType = new ArrayList<>();
            parameterType.add("com.sankuai.wmarch.map.thriftClient.route.RouteRequest");

            // 2.构造步行路线请求参数
            Map<String, Object> walkingRequest = new HashMap<>();

            Map<String, Object> originMap = new HashMap<>();
            originMap.put("location", origin);
            walkingRequest.put("origin", originMap);

            Map<String, Object> destinationMap = new HashMap<>();
            destinationMap.put("location", destination);
            walkingRequest.put("destination", destinationMap);

            // 注意：这里没有传key，使用服务默认的key
            walkingRequest.put("show_fields", "duration");

            List<String> parameterArgs = new ArrayList<>();
            parameterArgs.add(JacksonUtils.simpleSerialize(walkingRequest));

            // 3.获取Thrift接口实例
            RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                    "com.sankuai.map.open.platform.api.MapOpenApiService",
                    "com.sankuai.apigw.map.facadecenter",
                    5000,
                    null,
                    null);

            // 4.调用Thrift接口
            String jsonString = rpcProcessor.invoke("walking", parameterType, parameterArgs);

            LOGGER.info("{} walking response = {}", LOG_PREFIX, jsonString);

            // 5.解析返回结果
            RouteGuideResult routeGuideResult = new RouteGuideResult();
            routeGuideResult.setType(WALKING_TYPE);

            List<RouteResult> routeResults = new ArrayList<>();
            List<Map> routes = FreeMarkerParser.convert2ListByPath(jsonString, "route", Map.class);

            if (routes != null && !routes.isEmpty()) {
                for (Map route : routes) {
                    RouteResult routeResult = new RouteResult();

                    // 设置详细路线信息（非批量模式）
                    routeResult.setOrigin(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "origin"));
                    routeResult.setDestination(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "destination"));

                    Double distance = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(route), "distance", 0.0);
                    Double duration = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(route), "duration", 0.0);

                    routeResult.setDistance(distance);
                    routeResult.setDuration(duration);

                    routeResults.add(routeResult);
                }
            }

            routeGuideResult.setRouteResults(routeResults);
            results.add(routeGuideResult);

        } catch (Exception e) {
            LOGGER.error("{} addWalkingRoute failed", LOG_PREFIX, e);
            // 步行路线获取失败不影响驾车路线，继续执行
        }
    }

    @Data
    public static class InputParam {
        @Parameter(name="起始点坐标")
        private String origin;

        @Parameter(name="终点坐标")
        private String destination;
    }

    @Data
    public static class OutputParam {
        @Parameter(name="路线规划结果列表")
        private List<RouteGuideResult> routeResults;
    }

    @Data
    public static class RouteGuideResult {
        @Parameter(name="路线类型")
        private String type;

        @Parameter(name="路线结果列表")
        private List<RouteResult> routeResults;
    }

    @Data
    public static class RouteResult {
        @Parameter(name="起始点")
        private String origin;

        @Parameter(name="终点")
        private String destination;

        @Parameter(name="用时（秒）")
        private Double duration;

        @Parameter(name="距离（米）")
        private Double distance;

        @Parameter(name="公交信息")
        private TransitInfo transitInfo;
    }

    @Data
    public static class TransitInfo {
        @Parameter(name="交通类型")
        private Integer transitType;

        @Parameter(name="交通工具名称")
        private String transitName;

        @Parameter(name="站点名称")
        private String stationName;

        @Parameter(name="步行距离")
        private Double walkingDistance;

        @Parameter(name="步行用时")
        private Double walkingDuration;
    }
}
