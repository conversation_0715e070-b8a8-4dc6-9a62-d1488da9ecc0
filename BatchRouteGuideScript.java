package com.sankuai.aitool.runtime.script;

import com.sankuai.aitool.runtime.engine.annotation.Parameter;
import com.sankuai.aitool.runtime.engine.model.JavaEngineCode;
import com.sankuai.aitool.runtime.engine.model.Response;
import com.sankuai.aitool.runtime.engine.rpc.RpcProcessor;
import com.sankuai.aitool.runtime.engine.rpc.impl.RpcRrocessorFactory;
import com.sankuai.aitool.runtime.engine.script.AircraftScriptExcutor;
import com.sankuai.aitool.runtime.engine.util.JacksonUtil;
import com.sankuai.aitool.runtime.engine.util.json.FreeMarkerParser;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

public class BatchRouteGuideScript implements AircraftScriptExcutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AircraftScriptExcutor.class);
    private static final String LOG_PREFIX = "[AC_Java_BatchRouteGuide] ";
    
    // 线程池用于并行处理
    private static ThreadPool threadPool = Rhino.newThreadPool("AC-BatchRouteGuide", 
        DefaultThreadPoolProperties.Setter()
            .withCoreSize(30)
            .withMaxSize(100)
            .withMaxQueueSize(100));

    private static final String DRIVING_TYPE = "driving";
    private static final String WALKING_TYPE = "walking";
    private static final String TRANSIT_TYPE = "transit";
    private static final String RIDING_TYPE = "riding";

    // 公交路线规划策略
    private static final String STRATEGY_DEFAULT = "STRATEGY_DEFAULT";
    
    // 路线展示字段
    private static final String SHOW_FIELDS = "duration";

    @Override
    public void excutor(String paramJson, Response response) {
        try {
            LOGGER.info("{} start processing, paramJson = {}", LOG_PREFIX, paramJson);
            
            // 解析输入参数
            String key = FreeMarkerParser.parse(paramJson, "key");
            List<String> types = FreeMarkerParser.convert2ListByPath(paramJson, "types", String.class);
            List<Map> routeGuideRequestList = FreeMarkerParser.convert2ListByPath(paramJson, "routeGuideRequestList", Map.class);
            
            LOGGER.info("{} parameters: key={}, types={}, requestList size={}", 
                    LOG_PREFIX, key, types, routeGuideRequestList != null ? routeGuideRequestList.size() : 0);
            
            // 检查参数
            if (routeGuideRequestList == null || routeGuideRequestList.isEmpty() || types == null || types.isEmpty()) {
                OutputParam outputParam = new OutputParam();
                outputParam.setRouteResults(new HashMap<>());
                response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
                return;
            }
            
            // 批量处理每个路由请求
            Map<String, List<RouteGuideResult>> resultMap = new ConcurrentHashMap<>();
            
            for (Map requestMap : routeGuideRequestList) {
                try {
                    String index = String.valueOf(requestMap.get("index"));
                    String origin = String.valueOf(requestMap.get("origin"));
                    String destination = String.valueOf(requestMap.get("destination"));
                    
                    LOGGER.info("{} processing request: index={}, origin={}, destination={}", 
                            LOG_PREFIX, index, origin, destination);
                    
                    List<RouteGuideResult> results = new ArrayList<>();
                    
                    // 根据types处理不同类型的路由请求
                    if (types.contains(DRIVING_TYPE)) {
                        processDrivingRoute(key, origin, destination, results);
                    }
                    
                    if (types.contains(WALKING_TYPE)) {
                        processWalkingRoute(key, origin, destination, results);
                    }
                    
                    if (types.contains(TRANSIT_TYPE)) {
                        processTransitRoute(key, origin, destination, results);
                    }
                    
                    if (types.contains(RIDING_TYPE)) {
                        processRidingRoute(key, origin, destination, results);
                    }
                    
                    resultMap.put(index, results);
                } catch (Exception e) {
                    LOGGER.error("{} process request failed, request={}, e={}", 
                            LOG_PREFIX, requestMap, e.getMessage());
                    // 为失败的请求返回空列表
                    resultMap.put(String.valueOf(requestMap.get("index")), new ArrayList<>());
                }
            }
            
            // 组装返回结果
            OutputParam outputParam = new OutputParam();
            outputParam.setRouteResults(resultMap);

            // 设置返回值
            response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
            LOGGER.info("{} response = {}", LOG_PREFIX, JacksonUtil.toJsonStrWithEmptyDefault(response));
        } catch (Exception e) {
            LOGGER.error("{} invoke failed e = {}", LOG_PREFIX, e);
            response.setJavaEngineCode(JavaEngineCode.SERVER_INNER_ERROR, e.getMessage());
        }
    }
    
    // 处理驾车路线
    private void processDrivingRoute(String key, String origin, String destination, List<RouteGuideResult> results) throws Exception {
        LOGGER.info("{} processing driving route: origin={}, destination={}", LOG_PREFIX, origin, destination);
        
        // 1.设置Thrift接口入参类型列表
        List<String> parameterType = new ArrayList<>();
        parameterType.add("com.sankuai.map.open.platform.api.driving.DrivingRouteRequest");

        // 2.构造DrivingRouteRequest对象
        Map<String, Object> originMap = new HashMap<>();
        originMap.put("location", origin);
        
        Map<String, Object> destinationMap = new HashMap<>();
        destinationMap.put("location", destination);
        
        Map<String, Object> drivingRouteRequest = new HashMap<>();
        drivingRouteRequest.put("key", key);
        drivingRouteRequest.put("origin", originMap);
        drivingRouteRequest.put("destination", destinationMap);
        drivingRouteRequest.put("show_fields", SHOW_FIELDS);
        
        // 3.设置Thrift接口入参值列表
        List<String> parameterArgs = new ArrayList<>();
        parameterArgs.add(JacksonUtils.simpleSerialize(drivingRouteRequest));

        // 4.获取Thrift接口实例
        RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                "com.sankuai.map.open.platform.api.MapOpenApiService", 
                "com.sankuai.apigw.map.facadecenter", 
                5000, 
                null, 
                null);

        // 5.调用Thrift接口
        String jsonString = rpcProcessor.invoke("driving", parameterType, parameterArgs);
        
        LOGGER.info("{} thrift driving response: {}", LOG_PREFIX, jsonString);
        
        // 6.解析返回结果
        RouteGuideResult routeGuideResult = new RouteGuideResult();
        routeGuideResult.setType(DRIVING_TYPE);
        List<RouteResult> routeResults = new ArrayList<>();
        
        try {
            // 使用Jackson解析返回的DrivingRouteResponse
            Map drivingResponse = JacksonUtils.simpleDeserialize(jsonString, Map.class);
            
            if (drivingResponse != null && drivingResponse.containsKey("route")) {
                List routes = (List) drivingResponse.get("route");
                
                if (routes != null && !routes.isEmpty()) {
                    for (Object routeObj : routes) {
                        Map route = (Map) routeObj;
                        if (route != null) {
                            BatchRouteResult batchRouteResult = new BatchRouteResult();
                            
                            // 提取距离和时间信息
                            if (route.containsKey("distance")) {
                                double distance = Double.parseDouble(route.get("distance").toString());
                                batchRouteResult.setDistanceInt((int) Math.ceil(distance));
                            }
                            
                            if (route.containsKey("duration")) {
                                double duration = Double.parseDouble(route.get("duration").toString());
                                batchRouteResult.setDurationMinute((int) (duration / 60));
                            }
                            
                            routeResults.add(batchRouteResult);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.warn("{} parse driving response failed: {}", LOG_PREFIX, e.getMessage());
        }
        
        routeGuideResult.setRouteResults(routeResults);
        results.add(routeGuideResult);
    }
    
    // 处理步行路线
    private void processWalkingRoute(String key, String origin, String destination, List<RouteGuideResult> results) throws Exception {
        LOGGER.info("{} processing walking route: origin={}, destination={}", LOG_PREFIX, origin, destination);
        
        // 1.设置Thrift接口入参类型列表
        List<String> parameterType = new ArrayList<>();
        parameterType.add("com.sankuai.wmarch.map.thriftClient.route.RouteRequest");

        // 2.构造RouteRequest对象
        Map<String, Object> originMap = new HashMap<>();
        originMap.put("location", origin);
        
        Map<String, Object> destinationMap = new HashMap<>();
        destinationMap.put("location", destination);
        
        Map<String, Object> routeRequest = new HashMap<>();
        routeRequest.put("key", key);
        routeRequest.put("origin", originMap);
        routeRequest.put("destination", destinationMap);
        routeRequest.put("show_fields", SHOW_FIELDS);
        
        // 3.设置Thrift接口入参值列表
        List<String> parameterArgs = new ArrayList<>();
        parameterArgs.add(JacksonUtils.simpleSerialize(routeRequest));

        // 4.获取Thrift接口实例
        RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                "com.sankuai.map.open.platform.api.MapOpenApiService", 
                "com.sankuai.apigw.map.facadecenter", 
                5000, 
                null, 
                null);

        // 5.调用Thrift接口
        String jsonString = rpcProcessor.invoke("walking", parameterType, parameterArgs);
        
        LOGGER.info("{} thrift walking response: {}", LOG_PREFIX, jsonString);
        
        // 6.解析返回结果
        RouteGuideResult routeGuideResult = new RouteGuideResult();
        routeGuideResult.setType(WALKING_TYPE);
        List<RouteResult> routeResults = new ArrayList<>();
        
        try {
            // 使用Jackson解析返回的RouteResponse
            Map walkingResponse = JacksonUtils.simpleDeserialize(jsonString, Map.class);
            
            if (walkingResponse != null && walkingResponse.containsKey("route")) {
                List routes = (List) walkingResponse.get("route");
                
                if (routes != null && !routes.isEmpty()) {
                    for (Object routeObj : routes) {
                        Map route = (Map) routeObj;
                        if (route != null) {
                            BatchRouteResult batchRouteResult = new BatchRouteResult();
                            
                            // 提取距离和时间信息
                            if (route.containsKey("distance")) {
                                double distance = Double.parseDouble(route.get("distance").toString());
                                batchRouteResult.setDistanceInt((int) Math.ceil(distance));
                            }
                            
                            if (route.containsKey("duration")) {
                                double duration = Double.parseDouble(route.get("duration").toString());
                                batchRouteResult.setDurationMinute((int) Math.ceil(duration / 60));
                            }
                            
                            routeResults.add(batchRouteResult);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.warn("{} parse walking response failed: {}", LOG_PREFIX, e.getMessage());
        }
        
        routeGuideResult.setRouteResults(routeResults);
        results.add(routeGuideResult);
    }
    
    // 处理公交路线
    private void processTransitRoute(String key, String origin, String destination, List<RouteGuideResult> results) throws Exception {
        LOGGER.info("{} processing transit route: origin={}, destination={}", LOG_PREFIX, origin, destination);
        
        // 1.设置Thrift接口入参类型列表
        List<String> parameterType = new ArrayList<>();
        parameterType.add("com.sankuai.map.open.platform.api.transit.TransitRouteRequest");

        // 2.构造TransitRouteRequest对象
        Map<String, Object> originMap = new HashMap<>();
        originMap.put("location", origin);
        
        Map<String, Object> destinationMap = new HashMap<>();
        destinationMap.put("location", destination);
        
        Map<String, Object> transitRouteRequest = new HashMap<>();
        transitRouteRequest.put("key", key);
        transitRouteRequest.put("origin", originMap);
        transitRouteRequest.put("destination", destinationMap);
        transitRouteRequest.put("strategy", STRATEGY_DEFAULT);
        
        // 3.设置Thrift接口入参值列表
        List<String> parameterArgs = new ArrayList<>();
        parameterArgs.add(JacksonUtils.simpleSerialize(transitRouteRequest));

        // 4.获取Thrift接口实例
        RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                "com.sankuai.map.open.platform.api.MapOpenApiService", 
                "com.sankuai.apigw.map.facadecenter", 
                5000, 
                null, 
                null);

        // 5.调用Thrift接口
        String jsonString = rpcProcessor.invoke("transit", parameterType, parameterArgs);
        
        LOGGER.info("{} thrift transit response: {}", LOG_PREFIX, jsonString);
        
        // 6.解析返回结果
        RouteGuideResult routeGuideResult = new RouteGuideResult();
        routeGuideResult.setType(TRANSIT_TYPE);
        List<RouteResult> routeResults = new ArrayList<>();
        
        try {
            // 使用Jackson解析返回的TransitRouteResponse
            Map transitResponse = JacksonUtils.simpleDeserialize(jsonString, Map.class);
            
            if (transitResponse != null && transitResponse.containsKey("routes")) {
                List routes = (List) transitResponse.get("routes");
                
                if (routes != null && !routes.isEmpty()) {
                    for (Object routeObj : routes) {
                        Map route = (Map) routeObj;
                        if (route != null) {
                            BatchRouteResult batchRouteResult = new BatchRouteResult();
                            
                            // 提取距离和时间信息
                            if (route.containsKey("distance")) {
                                double distance = Double.parseDouble(route.get("distance").toString());
                                batchRouteResult.setDistanceInt((int) Math.ceil(distance));
                            }
                            
                            if (route.containsKey("duration")) {
                                double duration = Double.parseDouble(route.get("duration").toString());
                                batchRouteResult.setDurationMinute((int) Math.ceil(duration / 60));
                            }
                            
                            if (route.containsKey("label")) {
                                batchRouteResult.setLabel(route.get("label").toString());
                            }
                            
                            // 处理公交路线段信息
                            if (route.containsKey("segments")) {
                                List<TransitInfo> transitInfos = processTransitSegments(route);
                                batchRouteResult.setTransitInfoList(transitInfos);
                            }
                            
                            routeResults.add(batchRouteResult);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.warn("{} parse transit response failed: {}", LOG_PREFIX, e.getMessage());
        }
        
        routeGuideResult.setRouteResults(routeResults);
        results.add(routeGuideResult);
    }
    
    // 处理骑行路线
    private void processRidingRoute(String key, String origin, String destination, List<RouteGuideResult> results) throws Exception {
        LOGGER.info("{} processing riding route: origin={}, destination={}", LOG_PREFIX, origin, destination);
        
        // 1.设置Thrift接口入参类型列表
        List<String> parameterType = new ArrayList<>();
        parameterType.add("com.sankuai.wmarch.map.thriftClient.route.RouteRequest");

        // 2.构造RouteRequest对象
        Map<String, Object> originMap = new HashMap<>();
        originMap.put("location", origin);
        
        Map<String, Object> destinationMap = new HashMap<>();
        destinationMap.put("location", destination);
        
        Map<String, Object> routeRequest = new HashMap<>();
        routeRequest.put("key", key);
        routeRequest.put("origin", originMap);
        routeRequest.put("destination", destinationMap);
        routeRequest.put("show_fields", SHOW_FIELDS);
        
        // 3.设置Thrift接口入参值列表
        List<String> parameterArgs = new ArrayList<>();
        parameterArgs.add(JacksonUtils.simpleSerialize(routeRequest));

        // 4.获取Thrift接口实例
        RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                "com.sankuai.map.open.platform.api.MapOpenApiService", 
                "com.sankuai.apigw.map.facadecenter", 
                5000, 
                null, 
                null);

        // 5.调用Thrift接口
        String jsonString = rpcProcessor.invoke("riding", parameterType, parameterArgs);
        
        LOGGER.info("{} thrift riding response: {}", LOG_PREFIX, jsonString);
        
        // 6.解析返回结果
        RouteGuideResult routeGuideResult = new RouteGuideResult();
        routeGuideResult.setType(RIDING_TYPE);
        List<RouteResult> routeResults = new ArrayList<>();
        
        try {
            // 使用Jackson解析返回的RouteResponse
            Map ridingResponse = JacksonUtils.simpleDeserialize(jsonString, Map.class);
            
            if (ridingResponse != null && ridingResponse.containsKey("route")) {
                List routes = (List) ridingResponse.get("route");
                
                if (routes != null && !routes.isEmpty()) {
                    for (Object routeObj : routes) {
                        Map route = (Map) routeObj;
                        if (route != null) {
                            BatchRouteResult batchRouteResult = new BatchRouteResult();
                            
                            // 提取距离和时间信息
                            if (route.containsKey("distance")) {
                                double distance = Double.parseDouble(route.get("distance").toString());
                                batchRouteResult.setDistanceInt((int) Math.ceil(distance));
                            }
                            
                            if (route.containsKey("duration")) {
                                double duration = Double.parseDouble(route.get("duration").toString());
                                batchRouteResult.setDurationMinute((int) Math.ceil(duration / 60));
                            }
                            
                            routeResults.add(batchRouteResult);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.warn("{} parse riding response failed: {}", LOG_PREFIX, e.getMessage());
        }
        
        routeGuideResult.setRouteResults(routeResults);
        results.add(routeGuideResult);
    }
    
    // 处理公交路线段信息
    private List<TransitInfo> processTransitSegments(Map route) {
        List<TransitInfo> transitInfos = new ArrayList<>();
        
        try {
            List segments = (List) route.get("segments");
            if (segments != null) {
                for (Object segmentsObj : segments) {
                    Map segmentsMap = (Map) segmentsObj;
                    if (segmentsMap != null && segmentsMap.containsKey("segment")) {
                        List segmentList = (List) segmentsMap.get("segment");
                        
                        if (segmentList != null && !segmentList.isEmpty()) {
                            for (Object segmentObj : segmentList) {
                                Map segment = (Map) segmentObj;
                                if (segment != null) {
                                    BatchTransitInfo transitInfo = new BatchTransitInfo();
                                    
                                    // 设置公交类型
                                    if (segment.containsKey("type")) {
                                        int type = Integer.parseInt(segment.get("type").toString());
                                        transitInfo.setTransitType(type);
                                    }
                                    
                                    // 设置距离
                                    if (segment.containsKey("distance")) {
                                        double distance = Double.parseDouble(segment.get("distance").toString());
                                        transitInfo.setDistanceInt((int) Math.ceil(distance));
                                    }
                                    
                                    // 设置时间
                                    if (segment.containsKey("duration")) {
                                        double duration = Double.parseDouble(segment.get("duration").toString());
                                        transitInfo.setDurationMinute((int) Math.ceil(duration / 60));
                                    }
                                    
                                    // 如果不是步行类型(type != 3)，则设置公交信息
                                    if (segment.containsKey("type") && !segment.get("type").toString().equals("3")) {
                                        // 设置线路名称
                                        if (segment.containsKey("line_info")) {
                                            Map lineInfo = (Map) segment.get("line_info");
                                            if (lineInfo != null && lineInfo.containsKey("name")) {
                                                transitInfo.setName(lineInfo.get("name").toString());
                                            }
                                        }
                                        
                                        // 设置站点数量
                                        if (segment.containsKey("station_cnt")) {
                                            transitInfo.setStationCnt(Integer.parseInt(segment.get("station_cnt").toString()));
                                        }
                                        
                                        // 设置站点名称列表
                                        if (segment.containsKey("stations")) {
                                            List stations = (List) segment.get("stations");
                                            if (stations != null) {
                                                List<String> stationNames = new ArrayList<>();
                                                for (Object stationObj : stations) {
                                                    Map station = (Map) stationObj;
                                                    if (station != null && station.containsKey("name")) {
                                                        stationNames.add(station.get("name").toString());
                                                    }
                                                }
                                                transitInfo.setStations(stationNames);
                                            }
                                        }
                                    }
                                    
                                    transitInfos.add(transitInfo);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.warn("{} process transit segments failed: {}", LOG_PREFIX, e.getMessage());
        }
        
        return transitInfos;
    }
    
    @Data
    public static class InputParam {
        @Parameter(name="服务权限标识")
        private String key;
        
        @Parameter(name="路由请求列表")
        private List<BatchRouteGuideRequest> routeGuideRequestList;
        
        @Parameter(name="路由类型列表")
        private List<String> types;
    }
    
    @Data
    public static class OutputParam {
        @Parameter(name="批量路由结果")
        private Map<String, List<RouteGuideResult>> routeResults;
    }
    
    @Data
    public static class BatchRouteGuideRequest {
        @Parameter(name="索引")
        private String index;
        
        @Parameter(name="起点坐标")
        private String origin;
        
        @Parameter(name="终点坐标")
        private String destination;
    }
    
    @Data
    public static class RouteGuideResult {
        @Parameter(name="路由类型")
        private String type;
        
        @Parameter(name="路由结果列表")
        private List<RouteResult> routeResults;
    }
    
    @Data
    public static class RouteResult {
        @Parameter(name="起点")
        private String origin;
        
        @Parameter(name="终点")
        private String destination;
        
        @Parameter(name="耗时(秒)")
        private Double duration;
        
        @Parameter(name="距离(米)")
        private Double distance;
        
        @Parameter(name="公交信息")
        private TransitInfo transitInfo;
    }
    
    @Data
    public static class BatchRouteResult extends RouteResult {
        @Parameter(name="距离(米,整数)")
        private Integer distanceInt;
        
        @Parameter(name="耗时(分钟,整数)")
        private Integer durationMinute;
        
        @Parameter(name="标签")
        private String label;
        
        @Parameter(name="公交信息列表")
        private List<TransitInfo> transitInfoList;
    }
    
    @Data
    public static class TransitInfo {
        @Parameter(name="公交类型")
        private Integer transitType;
        
        @Parameter(name="步行距离")
        private Double walkingDistance;
        
        @Parameter(name="步行时间")
        private Double walkingDuration;
        
        @Parameter(name="公交名称")
        private String transitName;
        
        @Parameter(name="站点名称")
        private String stationName;
    }
    
    @Data
    public static class BatchTransitInfo extends TransitInfo {
        @Parameter(name="距离(米,整数)")
        private Integer distanceInt;
        
        @Parameter(name="耗时(分钟,整数)")
        private Integer durationMinute;
        
        @Parameter(name="名称")
        private String name;
        
        @Parameter(name="站点数量")
        private Integer stationCnt;
        
        @Parameter(name="站点列表")
        private List<String> stations;
    }
}
