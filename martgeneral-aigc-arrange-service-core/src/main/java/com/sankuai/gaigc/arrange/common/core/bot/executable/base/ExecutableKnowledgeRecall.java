/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.executable.base;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotProcessCodeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotSourceEnum;
import com.sankuai.gaigc.arrange.common.core.bot.executable.IExecutable;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep;
import com.sankuai.gaigc.arrange.common.core.bot.service.BotKnowledgeRecallService;
import com.sankuai.gaigc.arrange.common.enums.AgentModeEnum;
import com.sankuai.gaigc.arrange.config.MccConfig;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * 知识召回
 *
 * <AUTHOR>
 * @created 2024/5/29
 */
@Slf4j
@Component
public class ExecutableKnowledgeRecall implements IExecutable {
    /** 执行器结果字段 */
    public static final String DATA_FIELD = "data";
    @Resource(name = "simpleKnowledgeRecallServiceImpl")
    private BotKnowledgeRecallService simpleKnowledgeRecallService;
    @Resource(name = "knowledgeCubeRecallServiceImpl")
    private BotKnowledgeRecallService knowledgeCubeRecallService;
    @Resource
    private MccConfig mccConfig;

    @Override
    public ExecuteResult execute(ExecuteStep step, ExecutePlanContext context) {
        BotKnowledgeRecallResult recallResult = recall(context);

        // 执行器结果字段
        Map<String, Object> finalResult = Maps.newHashMap();
        finalResult.put(DATA_FIELD, recallResult);

        ExecuteResult result = new ExecuteResult(true, step.getObjective(), finalResult, SymbolConstant.EMPTY);
        result.setRecall(BooleanUtils.isFalse(isEmpty(recallResult.getOriginRecallResult())));
        result.setProcessCodes(recallResult.getProcessCodes());
        return result;
    }

    @Override
    public BotExecutorTypeEnum type() {
        return BotExecutorTypeEnum.KNOWLEDGE;
    }

    @Override
    public Set<AgentModeEnum> applyMode() {
        return Sets.newHashSet(AgentModeEnum.ALL);
    }

    private BotKnowledgeRecallResult recall(ExecutePlanContext context) {
        try {
            AIBot bot = context.getBot();

            if (useKnowledgeCube(bot)) {
                return knowledgeCubeRecallService.recall(context);
            }
            return simpleKnowledgeRecallService.recall(context);
        } catch (Exception e) {
            log.error("知识库召回出错, botId:{}, e=", context.getBot().getId(), e);
            BotKnowledgeRecallResult recallResult = new BotKnowledgeRecallResult();
            recallResult.setOriginRecallResult(Collections.emptyList());
            recallResult.setPlaceHolderAndContent(Collections.emptyMap());
            recallResult.getProcessCodes().add(BotProcessCodeEnum.KNOWLEDGE_RECALL_ERROR.getValue());
            return recallResult;
        }
    }

    private boolean isEmpty(List<KnowledgeBaseRetrieveResult> originRecallResult) {
        if (CollectionUtils.isEmpty(originRecallResult)) {
            return true;
        }
        for (KnowledgeBaseRetrieveResult retrieveResult : originRecallResult) {
            if (CollectionUtils.isNotEmpty(retrieveResult.getResult())) {
                return false;
            }
        }
        return true;
    }

    private boolean useKnowledgeCube(AIBot bot) {
        BotSourceEnum botSource = bot.getBotSource();
        if (BotSourceEnum.K_CUBE == botSource) {
            return true;
        }
        if (CollectionUtils.isEmpty(bot.getKnowledgeBases())) {
            return false;
        }
        List<Long> knowledgeBaseIds = bot.getKnowledgeBases().stream().map(KnowledgeBase::getId).collect(Collectors.toList());
        if (knowledgeBaseIds.size() == 1 && Objects.equals(knowledgeBaseIds.get(0), mccConfig.getKCubeKnowledgeBaseId())) {
            return true;
        }
        return false;
    }

}
