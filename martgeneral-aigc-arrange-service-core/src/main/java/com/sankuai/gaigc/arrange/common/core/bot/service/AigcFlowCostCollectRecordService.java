package com.sankuai.gaigc.arrange.common.core.bot.service;


import com.sankuai.gaigc.arrange.common.core.promptflow.entity.aigc.flow.cost.collect.QueryByFlowIdAppIdModelNameDTO;
import com.sankuai.gaigc.arrange.dao.dal.entity.AigcFlowCostCollectRecordDO;

import java.util.List;

public interface AigcFlowCostCollectRecordService {

    int recordUsageAndCost(List<AigcFlowCostCollectRecordDO> aigcFlowCostCollectRecordDOList);

    List<AigcFlowCostCollectRecordDO> queryByFlowIdAppIdModelName(QueryByFlowIdAppIdModelNameDTO queryByFlowIdAppIdModelNameDTO);

    int updateById(List<AigcFlowCostCollectRecordDO> updateByIdList);

    int insert(List<AigcFlowCostCollectRecordDO> insertList);
}
