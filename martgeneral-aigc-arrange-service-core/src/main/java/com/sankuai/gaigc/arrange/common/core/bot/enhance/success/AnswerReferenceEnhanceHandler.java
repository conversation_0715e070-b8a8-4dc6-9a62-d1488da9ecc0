/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.enhance.success;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.AnswerReferenceEnhance;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.AnswerReferenceKnowledge;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.BotEnhanceItem;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.enhance.BotSuccessEnhanceHandler;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableKnowledgeRecall;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService;
import com.sankuai.gaigc.arrange.common.core.bot.service.impl.KnowledgeCubeRecallServiceImpl;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import com.sankuai.gaigc.arrange.common.util.VectorUtil;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.config.MccConfig;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseField;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeResultItem;
import com.sankuai.gaigc.arrange.dao.dal.enums.KnowledgeBaseFieldTypeEnum;
import com.sankuai.gaigc.arrange.remote.basicapi.ContentEmbeddingRemoteService;
import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * 聊天增强：引用和归属
 *
 * <AUTHOR>
 * @created 2024/8/16
 */
@Slf4j
@Component
public class AnswerReferenceEnhanceHandler implements BotSuccessEnhanceHandler {
    private static final String KNOWLEDGE_FIELD_CATEGORY = "category";
    private static final String EBD_MODEL = "shibing624/text2vec-base-chinese";
    @Resource
    private AIBotKnowledgeBaseService knowledgeBaseService;
    @Resource
    private ContentEmbeddingRemoteService contentEmbeddingRemoteService;
    @Resource
    private MccConfig mccConfig;

    @Override
    public void handle(BotEnhanceItem enhanceConfig, BotRunResult runResult, BotRunParam runParam, AIBot bot) {
        if (!(enhanceConfig instanceof AnswerReferenceEnhance) || StringUtils.isBlank(runResult.getReplyContent()) || Objects.isNull(runResult.getPlan())) {
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        List<ExecuteStep> knowledgeSteps = runResult.getPlan().getExecutionSteps().stream()
                .flatMap(item -> item.getSteps().stream())
                .filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.KNOWLEDGE.getType()))
                .filter(item -> item.getExecuteResult().isRecall()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(knowledgeSteps)) {
            return;
        }
        List<KnowledgeBaseRetrieveResult> retrieveResults = knowledgeSteps.stream()
                .map(item -> item.getExecuteResult().getResult().get(ExecutableKnowledgeRecall.DATA_FIELD))
                .filter(Objects::nonNull).map(item -> (BotKnowledgeRecallResult) item)
                .flatMap(item -> item.getOriginRecallResult().stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(retrieveResults)) {
            return;
        }
        List<Double> queryEmbedding = contentEmbeddingRemoteService.textEmbeddingUseDouble(runResult.getReplyContent(), EBD_MODEL);
        List<AnswerReferenceKnowledge> answerReferenceKnowledge = generateReferenceKnowledge(queryEmbedding, mergeSameKnowledgeBaseData(retrieveResults), (AnswerReferenceEnhance) enhanceConfig);
        runResult.getEnhanceResult().setReferenceKnowledge(answerReferenceKnowledge);
        if (CollectionUtils.isEmpty(answerReferenceKnowledge) || !StreamInfoHolder.availableStream(runParam.getStream())) {
            return;
        }
        // 发送流式消息
        stopWatch.stop();
        SSEMessage sseMessage = SSEMessageUtils.generateAnswerReferenceMessage(answerReferenceKnowledge, stopWatch.getTotalTimeSeconds(), SSEMessageUtils.generateSectionId());
        try {
            StreamInfoHolder.getEmitter().send(sseMessage);
        } catch (IOException e) {
            log.error("流式输出出错, botId:{}, e=", bot.getId(), e);
        }
    }

    @Override
    public String relateConfigClassName() {
        return AnswerReferenceEnhance.class.getSimpleName();
    }

    private List<AnswerReferenceKnowledge> generateReferenceKnowledge(List<Double> queryEmbedding, List<KnowledgeBaseRetrieveResult> retrieveResults, AnswerReferenceEnhance answerReferenceEnhance) {
        return retrieveResults.stream()
                .flatMap(item -> generateReferenceKnowledge(queryEmbedding, item).stream())
                .filter(item -> item.getReferenceScore() >= answerReferenceEnhance.getScore())
                .sorted(Comparator.comparing(AnswerReferenceKnowledge::getReferenceScore))
                .limit(answerReferenceEnhance.getTopK())
                .collect(Collectors.toList());
    }

    private List<AnswerReferenceKnowledge> generateReferenceKnowledge(List<Double> queryEmbedding, KnowledgeBaseRetrieveResult retrieveResult) {
        boolean isCubeKnowledge = Objects.equals(mccConfig.getKCubeKnowledgeBaseId(), retrieveResult.getKnowledgeBaseId());
        List<KnowledgeBase> knowledgeBases = isCubeKnowledge ? null : knowledgeBaseService.queryByIds(Lists.newArrayList(retrieveResult.getKnowledgeBaseId()));
        KnowledgeBase thisKnowledgeBase = CollectionUtils.isEmpty(knowledgeBases) ? null : knowledgeBases.get(0);
        List<AnswerReferenceKnowledge> referenceKnowledgeList = retrieveResult.getResult().stream()
                .map(item -> {
                    AnswerReferenceKnowledge referenceKnowledge = new AnswerReferenceKnowledge();
                    referenceKnowledge.setKnowledgeBaseId(retrieveResult.getKnowledgeBaseId());
                    referenceKnowledge.setKnowledgeBaseName(retrieveResult.getKnowledgeBaseName());
                    referenceKnowledge.setKnowledgeContent(isCubeKnowledge ? convertCubeKnowledge(item) : convertStandardKnowledge(item, thisKnowledgeBase));
                    referenceKnowledge.setDocId(isCubeKnowledge ? convertCubeDocId(item) : item.getDocId());
                    referenceKnowledge.setFieldData(item.getFieldData());
                    referenceKnowledge.setCategory(isCubeKnowledge ? convertCubeKnowledgeCategory(item) : Collections.emptyList());
                    referenceKnowledge.setHash(item.getHash());
                    return referenceKnowledge;
                }).collect(Collectors.toList());
        // 计算相似度
        for (AnswerReferenceKnowledge referenceKnowledge : referenceKnowledgeList) {
            String knowledgeContent = referenceKnowledge.getKnowledgeContent();
            if (StringUtils.isBlank(knowledgeContent)) {
                continue;
            }
            List<Double> knowledgeEmbedding = contentEmbeddingRemoteService.textEmbeddingUseDouble(knowledgeContent, EBD_MODEL);
            referenceKnowledge.setReferenceScore(VectorUtil.cosineSimilarity(queryEmbedding, knowledgeEmbedding));
        }
        return referenceKnowledgeList;
    }

    private String convertCubeKnowledge(KnowledgeResultItem data) {
        String segmentContent = KnowledgeBaseFieldTypeEnum.STRING.getStringFieldValue(data.getFieldData(), KnowledgeCubeRecallServiceImpl.FIELD_SEGMENT_CONTENT);
        String answer = KnowledgeBaseFieldTypeEnum.STRING.getStringFieldValue(data.getFieldData(), "answer");
        if (StringUtils.isNotBlank(segmentContent)) {
            return segmentContent;
        } else if (StringUtils.isNotBlank(answer)) {
            return answer;
        }
        return GsonUtil.toJson(data.getFieldData());
    }

    private Long convertCubeDocId(KnowledgeResultItem data) {
        String segmentContent = KnowledgeBaseFieldTypeEnum.STRING.getStringFieldValue(data.getFieldData(), KnowledgeCubeRecallServiceImpl.FIELD_SEGMENT_CONTENT);
        String answer = KnowledgeBaseFieldTypeEnum.STRING.getStringFieldValue(data.getFieldData(), "answer");
        if (StringUtils.isNotBlank(segmentContent)) {
            return KnowledgeBaseFieldTypeEnum.LONG.getLongFieldValue(data.getFieldData(), "documentId");
        } else if (StringUtils.isNotBlank(answer)) {
            return KnowledgeBaseFieldTypeEnum.LONG.getLongFieldValue(data.getFieldData(), "qaId");
        }
        return null;
    }

    private List<Integer> convertCubeKnowledgeCategory(KnowledgeResultItem item) {
        Map<String, Object> filedNameAndValue = item.getFieldData();
        if (MapUtils.isEmpty(filedNameAndValue) || !filedNameAndValue.containsKey(KNOWLEDGE_FIELD_CATEGORY)) {
            return Collections.emptyList();
        }
        List<String> categoryStrList = KnowledgeBaseFieldTypeEnum.STRING_LIST.getStringListFieldValue(filedNameAndValue, KNOWLEDGE_FIELD_CATEGORY);
        if (CollectionUtils.isEmpty(categoryStrList)) {
            return Collections.emptyList();
        }
        return categoryStrList.stream().map(Integer::parseInt).collect(Collectors.toList());
    }

    private String convertStandardKnowledge(KnowledgeResultItem data, KnowledgeBase knowledgeBase) {
        if (Objects.isNull(knowledgeBase)) {
            return GsonUtil.toJson(data.getFieldData());
        }
        Set<String> fieldNames = knowledgeBase.getFields().stream().filter(KnowledgeBaseField::isAsEmbedding).map(KnowledgeBaseField::getFieldName).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(fieldNames)) {
            return GsonUtil.toJson(data.getFieldData());
        }
        List<String> fieldValues = Lists.newArrayList();
        for (Map.Entry<String, Object> entry : data.getFieldData().entrySet()) {
            String fieldName = entry.getKey();
            if (!fieldNames.contains(fieldName)) {
                continue;
            }
            fieldValues.add(KnowledgeBaseFieldTypeEnum.STRING.getStringFieldValue(data.getFieldData(), fieldName));
        }
        return StringUtils.join(fieldValues, SymbolConstant.NEW_LINE);
    }

    private List<KnowledgeBaseRetrieveResult> mergeSameKnowledgeBaseData(List<KnowledgeBaseRetrieveResult> retrieveResults) {
        Map<Long, KnowledgeBaseRetrieveResult> mergeData = Maps.newHashMap();

        Map<Long, KnowledgeBaseRetrieveResult> knowledgeBaseIdAndResult = retrieveResults.stream().collect(Collectors.toMap(KnowledgeBaseRetrieveResult::getKnowledgeBaseId, Function.identity()));
        for (Map.Entry<Long, KnowledgeBaseRetrieveResult> entry : knowledgeBaseIdAndResult.entrySet()) {
            Long knowledgeBaseId = entry.getKey();
            KnowledgeBaseRetrieveResult retrieveResult = entry.getValue();

            if (mergeData.containsKey(knowledgeBaseId)) {
                mergeData.get(knowledgeBaseId).getResult().addAll(retrieveResult.getResult());
                continue;
            }
            mergeData.put(knowledgeBaseId, retrieveResult);
        }
        return Lists.newArrayList(mergeData.values());
    }

}