package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.service.inf.kms.client.Kms;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.constant.ServiceConstant;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.function.friday.IFridayOperationRepository;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.dao.dal.external.eagle.IEagleRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.sankuai.gaigc.arrange.common.constant.ServiceConstant.APP_KEY;

/**
 * @author: zhangjiahui
 * @created: 2023/12/21
 * @description:
 */
@PromptFlowComponent(name = "GeneralEmbeddingDelete", desc = "通用向量化删除组件", type = ComponentTypeEnum.EMBEDDING_DELETE, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "cluster", desc = "集群名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "tenant", desc = "租户名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "accessKeyName", desc = "access信息-KMS秘钥名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "accessAppKey", desc = "access信息-KMS秘钥关联appKey", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "collection", desc = "索引名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "idList", desc = "向量id列表", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "isDeleteEagle", desc = "是否删除es中数据", type = ParamTypeEnum.BOOLEAN, category = CategoryEnum.COMMON_OPTION, required = false),
        @Param(name = "esIndexName", desc = "es索引名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false)
})
@OutputParamDefinition({@Param(name = "result", desc = "删除请求返回结果", type = ParamTypeEnum.STRING, required = true)
})
@Slf4j
public class GeneralEmbeddingDelete extends AbstractComponent {
    @Resource
    private IFridayOperationRepository fridayOperationRepository;

    @Resource
    private IEagleRepository eagleRepository;

    private static final String VECTOR_DATA_BASE_ACCESS_KEY  = Lion.getString(APP_KEY, "vector.access.key");

    /** 测试环境域名 */
    private static final String TEST_DOMAIN = "https://mvp.ai.test.sankuai.com";
    /** 上线环境域名 */
    private static final String PROD_DOMAIN = "https://mvp.sankuai.com";
    /** 删除数据接口路径 */
    private static final String DELETE_URL = "/mvp/facade/deleteIds";

    @Override
    public Map<String, Object> execute (Context context, ComponentInfo componentInfo) throws Exception {
        String url = getDomain() + DELETE_URL;
        Map<String, Object> paramMap = getParam(componentInfo);
        Map<String, String> headerMap = getHeader();
        boolean isDeleteEagle = Optional.ofNullable((Boolean) parseParam("isDeleteEagle", componentInfo)).orElse(false);
        HashMap<String, Object> result = Maps.newHashMap();
        List<?> idList = (List<?>) parseParam("idList", componentInfo);

        String fridayResponse = fridayOperationRepository.executeDeleteOperation(url, paramMap, headerMap);
        if (StringUtils.isEmpty(fridayResponse)){
            throw new Exception("Friday delete operation failed, paramMap = " + paramMap);
        }
        JSONObject jsonObject = JSONObject.parseObject(fridayResponse);
        if (jsonObject.get("code").equals(200)) {
            result.put("result", "success");
        } else {
            throw new Exception("Friday delete operation failed, idList = " + idList);
        }

        if (isDeleteEagle) {
            String esIndexName = (String) parseParam("esIndexName", componentInfo);
            eagleRepository.eagleDeleteOperation(esIndexName, idList);
        }
        return result;
    }
    private Map<String, Object> getParam(ComponentInfo componentInfo) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("cluster", parseParam("cluster", componentInfo));
        paramMap.put("collection", parseParam("collection", componentInfo));
        String tenant = (String) parseParam("tenant", componentInfo);
        paramMap.put("tenant", StringUtils.isNotEmpty(tenant) ? tenant : GeneralEmbeddingSearch.VECTOR_DATA_BASE_TENANT);
        paramMap.put("primaryIds", parseParam("idList", componentInfo));

        String accessKeyName = (String) parseParam("accessKeyName", componentInfo);
        String accessAppKey = (String) parseParam("accessAppKey", componentInfo);
        accessAppKey = StringUtils.isNotEmpty(accessAppKey) ? accessAppKey : ServiceConstant.APP_KEY;
        paramMap.put("accessKey", (StringUtils.isNotEmpty(accessKeyName) ? Kms.getByName(accessAppKey, accessKeyName) : VECTOR_DATA_BASE_ACCESS_KEY));
        return paramMap;
    }
    private Map<String, String> getHeader(){
        Map<String, String> header = new HashMap<>();
        header.put("Accept", "application/json");
        header.put("Accept-Language", "zh-CN,zh;q=0.9");
        header.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36");
        return header;
    }
    private String getDomain(){
        if (Environment.isTestEnv()){
            return TEST_DOMAIN;
        }
        return PROD_DOMAIN;
    }
}
