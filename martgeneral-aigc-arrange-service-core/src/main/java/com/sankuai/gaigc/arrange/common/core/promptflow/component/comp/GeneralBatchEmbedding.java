package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.constant.EmbeddingModelConstant;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ComponentConstants;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.RhinoUtils;
import com.sankuai.gaigc.arrange.common.util.HttpUtils;
import com.sankuai.gaigc.arrange.remote.basicapi.ContentEmbeddingRemoteService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;


@PromptFlowComponent(name = "GeneralBatchEmbedding", desc = "通用批量向量化组件", type = ComponentTypeEnum.EMBEDDING, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "querys", desc = "需要转换的文本列表", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "modelName", desc = "统一大模型服务的模型名", type = ParamTypeEnum.STRING, category = CategoryEnum.COMMON_OPTION, required = true)
})
@OutputParamDefinition({@Param(name = "embeddingVectors", desc = "输出向量列表", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true)})
public class GeneralBatchEmbedding extends AbstractComponent {

    private static final String OPEN_EMBEDDING_API = "https://aigc.sankuai.com/v1/openai/native/embeddings";

    @Resource
    private ContentEmbeddingRemoteService contentEmbeddingRemoteService;

    private static ExecutorService BATCH_EMBEDDING_EXECUTOR = TraceExecutors.getTraceExecutorService(
        Rhino.newThreadPool("batch-embedding-executor",
                DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(1000)
        ).getExecutor());

    @Override
    public Map<String, Object> execute (Context context, ComponentInfo componentInfo) throws Exception {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", ComponentConstants.DEFAULT_APP_ID);
        String modelName = (String) parseParam("modelName", componentInfo);

        List<String> querys = (List<String>) parseParam("querys", componentInfo);
        List<Callable<List<Float>>> tasks = new ArrayList<>();

        if(EmbeddingModelConstant.FRIDAY_MODELS.contains(modelName)){
            for (String query : querys) {
                Map<String, String> params = new HashMap<String, String>() {{
                    put("input", query);
                    put("model", modelName);
                }};

                tasks.add(() -> {
                    String response = HttpUtils.getPostResponse(OPEN_EMBEDDING_API, JSON.toJSONString(params), header);
                    JSONObject responseObject = JSON.parseObject(response);
                    JSONArray data = responseObject.getJSONArray("data");
                    JSONObject dataObject = data.getJSONObject(0);
                    JSONArray embeddingArray = dataObject.getJSONArray("embedding");
                    return embeddingArray.toJavaList(Float.class);
                });
            }
        }else{
            if(StringUtils.isBlank(modelName)){
                throw new UnsupportedOperationException("不支持的Embedding模型");
            }

            for (String query : querys) {
                tasks.add(() -> {
                    //RhinoUtils.acquire(modelName, null);
                    return contentEmbeddingRemoteService.textEmbedding(query, modelName);
                });
            }
        }
        List<Future<List<Float>>> futures = BATCH_EMBEDDING_EXECUTOR.invokeAll(tasks);

        List<Object> list = Lists.newArrayList();
        for (int i = 0; i < querys.size(); i++) {
            list.add(futures.get(i).get());
        }
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("embeddingVectors",list);

        return resultMap;
    }
}
