package com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance;

import lombok.Data;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@Data
public class ReviewDataResult {
    private Long reviewId;

    private LocalDate addTime;

    private Long originReviewId;

    private Integer source;

    public Map<String, Object> toMap() {
        Map<String, Object> result = new HashMap<>();
        if (addTime != null) {
            result.put("addTime", addTime.toString());
        }
        if (originReviewId != null) {
            result.put("originReviewId", originReviewId);
        }
        if (source != null) {
            result.put("source", source);
        }
        if (reviewId != null) {
            result.put("reviewId", reviewId);
        }
        return result;
    }
}
