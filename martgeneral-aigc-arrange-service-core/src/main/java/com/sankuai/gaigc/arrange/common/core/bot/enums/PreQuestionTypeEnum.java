package com.sankuai.gaigc.arrange.common.core.bot.enums;

/**
 * <AUTHOR>
 * @date 2024/11/11 10:28
 */
public enum PreQuestionTypeEnum {
    DEFAULT(1,"默认类型"),
    CUSTOM(2, "自定义类型"),
    FLOW_ACCESS(3, "工作流接入");

    /** 枚举值 */
    private Integer value;
    /** 枚举描述 */
    private String desc;

    PreQuestionTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static PreQuestionTypeEnum getEnumByValue(Integer value) {
        for (PreQuestionTypeEnum typeEnum : PreQuestionTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "PreQuestionTypeEnum{" +
                "value=" + value +
                ", desc='" + desc + '\'' +
                '}';
    }
}
