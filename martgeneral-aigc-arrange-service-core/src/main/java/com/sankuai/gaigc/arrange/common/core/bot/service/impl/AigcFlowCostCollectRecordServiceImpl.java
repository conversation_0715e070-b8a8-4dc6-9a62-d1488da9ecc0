package com.sankuai.gaigc.arrange.common.core.bot.service.impl;

import com.sankuai.gaigc.arrange.common.core.bot.service.AigcFlowCostCollectRecordService;
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.aigc.flow.cost.collect.QueryByFlowIdAppIdModelNameDTO;
import com.sankuai.gaigc.arrange.dao.dal.entity.AigcFlowCostCollectRecordDO;
import com.sankuai.gaigc.arrange.dao.dal.example.AigcFlowCostCollectRecordDOExample;
import com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFlowCostCollectRecordDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年05月28日14:31:33
 */
@Slf4j
@Service
public class AigcFlowCostCollectRecordServiceImpl implements AigcFlowCostCollectRecordService {

    @Resource
    private AigcFlowCostCollectRecordDOMapper aigcFlowCostCollectRecordDOMapper;
    /**
     * 根据flowId,appId,modelName 这三个字段去查询,如果不存在就insert, 如果存在,就累加 inputUsage outputUsage cost  值
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int recordUsageAndCost(List<AigcFlowCostCollectRecordDO> aigcFlowCostCollectRecordDOList) {
        if (CollectionUtils.isEmpty(aigcFlowCostCollectRecordDOList)) {
            return 0;
        }
        QueryByFlowIdAppIdModelNameDTO queryByFlowIdAppIdModelNameDTO = new QueryByFlowIdAppIdModelNameDTO().
                create(aigcFlowCostCollectRecordDOList);

        List<AigcFlowCostCollectRecordDO> aigcFlowCostCollectRecordDOS = this.queryByFlowIdAppIdModelName(queryByFlowIdAppIdModelNameDTO);

        Map<String, AigcFlowCostCollectRecordDO> appIdFlowIdModelNameKeyMap = aigcFlowCostCollectRecordDOS.stream()
                .collect(Collectors.toMap(this::convertAppIdFlowIdModelNameKey,
                                          Function.identity(), (k1, k2) -> k1
                ));

        List<AigcFlowCostCollectRecordDO> updateByIdList = new ArrayList<>();
        List<AigcFlowCostCollectRecordDO> insertList = new ArrayList<>();
        for (AigcFlowCostCollectRecordDO aigcFlowCostCollectRecordDO : aigcFlowCostCollectRecordDOList) {

            AigcFlowCostCollectRecordDO temp = appIdFlowIdModelNameKeyMap.get(this.convertAppIdFlowIdModelNameKey(aigcFlowCostCollectRecordDO));
            if (null == temp) {
                // 说明不存在库里面,直接往里面add
                aigcFlowCostCollectRecordDO.setIsDel(0);
                insertList.add(aigcFlowCostCollectRecordDO);
                continue;
            }
            // 说明在数据库已经存在了.
            //累加值准备往库里面update
            temp.setInputUsage(temp.getInputUsage() + aigcFlowCostCollectRecordDO.getInputUsage());
            temp.setOutputUsage(temp.getOutputUsage() + aigcFlowCostCollectRecordDO.getOutputUsage());
            temp.setCost(new BigDecimal(temp.getCost()).add(new BigDecimal(aigcFlowCostCollectRecordDO.getCost()))
                                 .toString());
            temp.setUpdateTime(new Date());
            updateByIdList.add(temp);
        }

        int updateEffectRow = updateById(updateByIdList);
        int insertEffectRow = insert(insertList);
        log.info("recordUsageAndCost updateEffectRow:{},insertEffectRow:{}", updateEffectRow, insertEffectRow);
        return updateEffectRow + insertEffectRow;
    }

    private String convertAppIdFlowIdModelNameKey(AigcFlowCostCollectRecordDO aigcFlowCostCollectRecordDO) {
        return aigcFlowCostCollectRecordDO.getAppId() + "-" + aigcFlowCostCollectRecordDO.getFlowId() + "-" + aigcFlowCostCollectRecordDO.getModelName();
    }

    @Override
    public List<AigcFlowCostCollectRecordDO> queryByFlowIdAppIdModelName(QueryByFlowIdAppIdModelNameDTO queryByFlowIdAppIdModelNameDTO) {
        if (queryByFlowIdAppIdModelNameDTO == null) {
            return new ArrayList<>();
        }
        List<Long> flowIdList = queryByFlowIdAppIdModelNameDTO.getFlowIdList();
        List<Long> appIdList = queryByFlowIdAppIdModelNameDTO.getAppIdList();
        List<String> modelNameList = queryByFlowIdAppIdModelNameDTO.getModelNameList();

        if (CollectionUtils.isEmpty(flowIdList) || CollectionUtils.isEmpty(appIdList) || CollectionUtils.isEmpty(modelNameList)) {
            return new ArrayList<>();
        }
        AigcFlowCostCollectRecordDOExample example = new AigcFlowCostCollectRecordDOExample();
        example.createCriteria()
                .andFlowIdIn(flowIdList)
                .andAppIdIn(appIdList)
                .andModelNameIn(modelNameList)
                .andIsDelEqualTo(0);
        return this.aigcFlowCostCollectRecordDOMapper.selectByExample(example);
    }

    @Override
    public int updateById(List<AigcFlowCostCollectRecordDO> updateByIdList) {
        if (CollectionUtils.isEmpty(updateByIdList)) {
            return 0;
        }
        return this.aigcFlowCostCollectRecordDOMapper.batchUpdateByPrimaryKey(updateByIdList);
    }

    @Override
    public int insert(List<AigcFlowCostCollectRecordDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        return this.aigcFlowCostCollectRecordDOMapper.batchInsert(insertList);
    }
}
