package com.sankuai.gaigc.arrange.common.core.bot.executable.mode;

import com.google.common.collect.Sets;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutableFunctionDirectParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutablePlaceHolderParam;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.executable.IExecutable;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ParamMatchDAG;
import com.sankuai.gaigc.arrange.common.enums.AgentModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * AgentModeEnum.PARAM_MATCH_EXECUTE模式下的占位符处理
 * <AUTHOR>
 * date 2024/7/8
 */
@Slf4j
@Component
public class ExecutableParamMatchPlaceHolder implements IExecutable {

    @Override
    public ExecuteResult execute(ExecuteStep step, ExecutePlanContext context) {
        ExecutablePlaceHolderParam param = (ExecutablePlaceHolderParam) step.getCustomParam();
        ParamMatchDAG graph = param.getGraph();
        ParamMatchDAG.Node placeHoderNode = param.getPlaceHoderNode();
        List<ParamMatchDAG.Node> dependNodes = graph.getData().get(placeHoderNode);
        ParamMatchDAG.Node dependNode = dependNodes.get(0); //展位符结点只会依赖一个其他结点

        List<ExecuteStep> steps = context.getSteps();

        if (dependNode.getType() == ParamMatchDAG.NodeType.FUNCTION) {
            Optional<ExecuteStep> first = steps.stream()
                    .filter(executeStep -> functionDirectStepMatch(executeStep, dependNode))
                    .findFirst();
            if (first.isPresent()) {
                return first.get().getExecuteResult();
            } else {
                log.error("未匹配到占位符执行结果{},botId={}", param.getPlaceHoderNode().getName(), context.getBot().getId());
                return new ExecuteResult(false, Collections.emptyMap(), "未查询到执行结果");
            }
        } else if (dependNode.getType() == ParamMatchDAG.NodeType.INPUT_PARAM) {
            Optional<ExecuteStep> first = steps.stream().filter(executeStep -> executeStep.getType().equals(BotExecutorTypeEnum.INPUT_HANDLER.getType())).findFirst();
            if (first.isPresent()) {
                Object res = first.get().getExecuteResult().getResult().get(param.getPlaceHoderNode().getName());
                Map<String, Object> result = new HashMap<>();
                result.put(param.getPlaceHoderNode().getName(), res);
                return new ExecuteResult(true, result, SymbolConstant.EMPTY);
            }
        }
        log.error("占位符依赖非function、非input，{},botId={}", param.getPlaceHoderNode().getName(), context.getBot().getId());

        return new ExecuteResult(false, Collections.emptyMap(), "不能依赖其他占位符");
    }

    @Override
    public BotExecutorTypeEnum type() {
        return BotExecutorTypeEnum.PLACE_HOLDER;
    }

    @Override
    public Set<AgentModeEnum> applyMode() {
        return Sets.newHashSet(AgentModeEnum.PARAM_MATCH_EXECUTE);
    }

    public boolean functionDirectStepMatch(ExecuteStep executeStep, ParamMatchDAG.Node node) {
        if (!executeStep.getType().equals(BotExecutorTypeEnum.FUNCTION_DIRECT.getType())) {
            return false;
        }
        ExecutableFunctionDirectParam customParam = (ExecutableFunctionDirectParam) executeStep.getCustomParam();
        return customParam.getFunctionNode().getFunction() == node.getFunction();
    }
}
