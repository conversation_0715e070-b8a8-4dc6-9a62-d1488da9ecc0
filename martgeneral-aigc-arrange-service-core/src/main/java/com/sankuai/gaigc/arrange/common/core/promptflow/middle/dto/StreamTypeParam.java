/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * GeneralLLM组件流式类型参数
 *
 * <AUTHOR>
 * @created 2024/12/17
 */
@Getter
@Setter
@ToString
public class StreamTypeParam extends GeneralLLMRequestParam {
    /** 执行步骤小节ID */
    private String sectionId;
    /** 工作流ID */
    private String flowId;
}