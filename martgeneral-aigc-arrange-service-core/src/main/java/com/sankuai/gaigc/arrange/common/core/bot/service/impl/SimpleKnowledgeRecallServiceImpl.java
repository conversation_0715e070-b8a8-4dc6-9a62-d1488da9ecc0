/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.nibhtp.os.htp.cms.access.common.search.Expression;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.CompParamsUtil;
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeBaseItem;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeBaseRecallExtConfig;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.UserModel;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotProcessCodeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotRecallModeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext;
import com.sankuai.gaigc.arrange.common.core.bot.service.BotKnowledgeAuthService;
import com.sankuai.gaigc.arrange.common.core.bot.service.BotKnowledgeProcessingService;
import com.sankuai.gaigc.arrange.common.core.bot.service.BotKnowledgeRecallService;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.proxy.IComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.CmsExpressionUtil;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeResultItem;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 简单的知识召回
 *
 * <AUTHOR>
 * @created 2024/5/31
 */
@Slf4j
@Service
public class SimpleKnowledgeRecallServiceImpl implements BotKnowledgeRecallService {
    private static final String PLACE_HOLDER_KNOWLEDGE = "knowledge";
    private static final String PLACE_HOLDER_KNOWLEDGE_BY_QUESTION = "knowledgeByQuestion";
    private static final String PLACE_HOLDER_KNOWLEDGE_BY_ANSWER = "knowledgeByAnswer";

    @Resource
    private IComponent knowledgeBaseRetrieve;
    @Resource(name = "crmSaleToolsAuthServiceImpl")
    private BotKnowledgeAuthService crmSaleToolsAuthService;
    @Resource(name = "botKnowledgeProcessingServiceImpl")
    private BotKnowledgeProcessingService botKnowledgeProcessingService;

    @Override
    public BotKnowledgeRecallResult recall(ExecutePlanContext context) {
        BotKnowledgeRecallResult recallResult = new BotKnowledgeRecallResult();
        AIBot bot = context.getBot();

        Map<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseIdAndRetrieveResult = Maps.newHashMap();
        List<BotKnowledgeBaseItem> botKnowledgeBase = Objects.nonNull(bot.getExtension()) ? bot.getExtension().getBotKnowledgeBase() : Lists.newArrayList();
        Map<Long, BotKnowledgeBaseItem> knowledgeBaseAndConfig = botKnowledgeBase.stream().collect(Collectors.toMap(BotKnowledgeBaseItem::getKnowledgeBaseId, Function.identity()));
        for (KnowledgeBase knowledgeBase : bot.getKnowledgeBases()) {
            // 查询Bot知识库配置
            BotKnowledgeBaseItem botKnowledgeBaseConfig = knowledgeBaseAndConfig.get(knowledgeBase.getId());
            if (Objects.isNull(botKnowledgeBaseConfig)) {
                botKnowledgeBaseConfig = generateDefaultConfig(knowledgeBase.getId());
                knowledgeBaseAndConfig.put(knowledgeBase.getId(), botKnowledgeBaseConfig);
            }
            // 召回单个知识库
            knowledgeBaseIdAndRetrieveResult.put(knowledgeBase.getId(), recallSingleKnowledgeBase(knowledgeBase, botKnowledgeBaseConfig, context));
        }
        recallResult.setOriginRecallResult(originRecallResult(bot, knowledgeBaseIdAndRetrieveResult));
        if (isEmpty(knowledgeBaseIdAndRetrieveResult)) {
            recallResult.getProcessCodes().add(BotProcessCodeEnum.RECALL_KNOWLEDGE_EMPTY.getValue());
            return recallResult;
        }
        // 根据相似度过滤
        Map<Long, Map<String, KnowledgeBaseRetrieveResult>> filterResult = filterBySimilarityThreshold(knowledgeBaseIdAndRetrieveResult, knowledgeBaseAndConfig);
        if (isEmpty(filterResult)) {
            recallResult.getProcessCodes().add(BotProcessCodeEnum.FILTER_SIMILARITY_EMPTY.getValue());
            return recallResult;
        }
        // 鉴权
        Map<Long, Map<String, KnowledgeBaseRetrieveResult>> authResult = knowledgeBaseAuth(filterResult, knowledgeBaseAndConfig, context.getRunParam().getUser());
        if (isEmpty(authResult)) {
            recallResult.getProcessCodes().add(BotProcessCodeEnum.KNOWLEDGE_NOT_AUTH_OR_AUTH_FAILURE.getValue());
            return recallResult;
        }
        // 加工
        Map<String, String> phAndKnowledge = botKnowledgeProcessingService.processingKnowledge(authResult, knowledgeBaseAndConfig);
        recallResult.setPlaceHolderAndContent(phAndKnowledge);

        if (!phAndKnowledge.containsKey(PLACE_HOLDER_KNOWLEDGE)) {
            String knowledge = String.join(SymbolConstant.NEW_LINE, phAndKnowledge.values());
            phAndKnowledge.put(PLACE_HOLDER_KNOWLEDGE, knowledge);
        }

        return recallResult;
    }

    private Map<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseAuth(Map<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseIdAndRetrieveResult, Map<Long, BotKnowledgeBaseItem> knowledgeBaseAndConfig, UserModel user) {
        if (MapUtils.isEmpty(knowledgeBaseIdAndRetrieveResult) || Objects.isNull(user) || MapUtils.isEmpty(knowledgeBaseAndConfig)) {
            return knowledgeBaseIdAndRetrieveResult;
        }

        Map<Long, Map<String, KnowledgeBaseRetrieveResult>> authResult = Maps.newHashMap();
        for (Map.Entry<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseEntry : knowledgeBaseIdAndRetrieveResult.entrySet()) {
            Long knowledgeBaseId = knowledgeBaseEntry.getKey();
            BotKnowledgeBaseItem knowledgeBaseConfig = knowledgeBaseAndConfig.get(knowledgeBaseId);
            if (Objects.isNull(knowledgeBaseConfig) || Objects.isNull(knowledgeBaseConfig.getAuthConfig()) || BooleanUtils.isNotTrue(knowledgeBaseConfig.getAuthConfig().getOpen())) {
                authResult.put(knowledgeBaseId, knowledgeBaseEntry.getValue());
                continue;
            }

            Map<String, KnowledgeBaseRetrieveResult> authPassData = crmSaleToolsAuthService.authData(knowledgeBaseEntry.getValue(), user, knowledgeBaseConfig.getAuthConfig());
            if (MapUtils.isEmpty(authPassData)) {
                authResult.put(knowledgeBaseId, authPassData);
            }
        }
        return authResult;
    }

    private Map<Long, Map<String, KnowledgeBaseRetrieveResult>> filterBySimilarityThreshold(Map<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseIdAndRetrieveResult, Map<Long, BotKnowledgeBaseItem> knowledgeBaseAndConfig) {
        if (MapUtils.isEmpty(knowledgeBaseIdAndRetrieveResult)) {
            return knowledgeBaseIdAndRetrieveResult;
        }

        Map<Long, Map<String, KnowledgeBaseRetrieveResult>> filterResult = Maps.newHashMap();
        for (Map.Entry<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseEntry : knowledgeBaseIdAndRetrieveResult.entrySet()) {
            Long knowledgeBaseId = knowledgeBaseEntry.getKey();
            BotKnowledgeBaseItem knowledgeBaseConfig = knowledgeBaseAndConfig.get(knowledgeBaseId);
            if (Objects.isNull(knowledgeBaseConfig) || Objects.isNull(knowledgeBaseConfig.getSimilarityThreshold())) {
                filterResult.put(knowledgeBaseId, knowledgeBaseEntry.getValue());
                continue;
            }

            Map<String, KnowledgeBaseRetrieveResult> retrieveResultMap = Maps.newHashMap();
            for (Map.Entry<String, KnowledgeBaseRetrieveResult> retrieveResultEntry : knowledgeBaseEntry.getValue().entrySet()) {
                String ph = retrieveResultEntry.getKey();
                List<KnowledgeResultItem> filterItems = retrieveResultEntry.getValue().getResult().stream().filter(item -> item.getMaxScore() > knowledgeBaseConfig.getSimilarityThreshold()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterItems)) {
                    continue;
                }
                KnowledgeBaseRetrieveResult retrieveResult = new KnowledgeBaseRetrieveResult();
                BeanUtils.copyProperties(retrieveResultEntry.getValue(), retrieveResult);
                for (KnowledgeResultItem filterItem : filterItems) {
                    filterItem.setHash(filterItem.generateHash());
                }
                retrieveResult.setResult(filterItems);
                retrieveResultMap.put(ph, retrieveResult);
            }
            filterResult.put(knowledgeBaseId, retrieveResultMap);
        }
        return filterResult;
    }

    private Map<String, KnowledgeBaseRetrieveResult> recallSingleKnowledgeBase(KnowledgeBase knowledgeBase, BotKnowledgeBaseItem botKnowledgeBaseConfig, ExecutePlanContext context) {
        // 召回模式
        BotKnowledgeBaseRecallExtConfig recallExtConfig = botKnowledgeBaseConfig.getRecallExtConfig();
        BotRecallModeEnum recallMode = Objects.nonNull(recallExtConfig) ? BotRecallModeEnum.getEnumByValue(recallExtConfig.getRecallMode()) : BotRecallModeEnum.TEXT_MODE;
        if (BotRecallModeEnum.TEXT_MODE == recallMode) {
            return textModeRecall(knowledgeBase, botKnowledgeBaseConfig, context);
        } else if (BotRecallModeEnum.QA_PAIR_MODE == recallMode) {
            return qaPairModeRecall(knowledgeBase, botKnowledgeBaseConfig, context);
        }
        throw new IllegalArgumentException("召回模式未知, config:" + GsonUtil.toJson(botKnowledgeBaseConfig));
    }

    private Map<String, KnowledgeBaseRetrieveResult> qaPairModeRecall(KnowledgeBase knowledgeBase, BotKnowledgeBaseItem knowledgeBaseConfig, ExecutePlanContext context) {
        Map<String, KnowledgeBaseRetrieveResult> placeHolderAndResult = Maps.newHashMap();
        // topK
        Integer topK = knowledgeBaseConfig.getTopK();
        Expression cmsScalarExp = knowledgeBaseConfig.getScalarFilterExp();
        // 根据问题召回
        String queryCondition = CmsExpressionUtil.buildAndFillQueryCondition(context.getRunParam(), cmsScalarExp, knowledgeBaseConfig.getRecallExtConfig().getQuestionExp());
        placeHolderAndResult.put(PLACE_HOLDER_KNOWLEDGE_BY_QUESTION, retrieveFromKnowledgeBase(context.getRunParam().getUserMsg(), topK, queryCondition, knowledgeBase, context.getBot().getId()));

        // 根据答案召回
        queryCondition = CmsExpressionUtil.buildAndFillQueryCondition(context.getRunParam(), cmsScalarExp, knowledgeBaseConfig.getRecallExtConfig().getAnswerExp());
        placeHolderAndResult.put(PLACE_HOLDER_KNOWLEDGE_BY_ANSWER, retrieveFromKnowledgeBase(context.getRunParam().getUserMsg(), topK, queryCondition, knowledgeBase, context.getBot().getId()));

        return placeHolderAndResult;
    }

    private Map<String, KnowledgeBaseRetrieveResult> textModeRecall(KnowledgeBase knowledgeBase, BotKnowledgeBaseItem knowledgeBaseConfig, ExecutePlanContext context) {
        // 转化表达式
        Expression cmsScalarExp = knowledgeBaseConfig.getScalarFilterExp();
        String queryCondition = CmsExpressionUtil.buildAndFillQueryCondition(context.getRunParam(), cmsScalarExp);
        // topK
        Integer topK = knowledgeBaseConfig.getTopK();

        Map<String, KnowledgeBaseRetrieveResult> placeHolderAndResult = Maps.newHashMap();
        KnowledgeBaseRetrieveResult retrieveResult = retrieveFromKnowledgeBase(context.getRunParam().getUserMsg(), topK, queryCondition, knowledgeBase, context.getBot().getId());
        placeHolderAndResult.put(PLACE_HOLDER_KNOWLEDGE, retrieveResult);
        return placeHolderAndResult;
    }

    private List<KnowledgeBaseRetrieveResult> originRecallResult(AIBot bot, Map<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseIdAndRetrieveResult) {
        return bot.getKnowledgeBases().stream().map(knowledgeBase -> {
            KnowledgeBaseRetrieveResult retrieveResult = new KnowledgeBaseRetrieveResult();
            retrieveResult.setKnowledgeBaseId(knowledgeBase.getId());
            retrieveResult.setKnowledgeBaseName(knowledgeBase.getName());
            if (knowledgeBaseIdAndRetrieveResult.containsKey(knowledgeBase.getId())) {
                List<KnowledgeResultItem> resultItems = knowledgeBaseIdAndRetrieveResult.get(knowledgeBase.getId()).values().stream().flatMap(knowledgeBaseRetrieveResult -> knowledgeBaseRetrieveResult.getResult().stream()).collect(Collectors.toList());
                retrieveResult.setResult(resultItems);
            } else {
                retrieveResult.setResult(Lists.newArrayList());
            }
            return retrieveResult;
        }).collect(Collectors.toList());
    }

    public KnowledgeBaseRetrieveResult retrieveFromKnowledgeBase(String query, Integer topK, String queryCondition, KnowledgeBase knowledgeBase, Long botId) {
        ComponentInfo componentInfo = new ComponentInfo();
        if (Objects.nonNull(botId)) {
            componentInfo.setBotId(String.valueOf(botId));
        }

        Map<String, Object[]> inputs = Maps.newHashMap();
        inputs.put("knowledgeBaseId", new Object[]{String.valueOf(knowledgeBase.getId()), ParamTypeEnum.STRING});
        inputs.put("query", new Object[]{query, ParamTypeEnum.STRING});
        inputs.put("topK", new Object[]{String.valueOf(Optional.ofNullable(topK).orElse(10)), ParamTypeEnum.INT});
        inputs.put("expression", new Object[]{queryCondition, ParamTypeEnum.STRING});
        componentInfo.setInputs(CompParamsUtil.buildInput(inputs));
        try {
            Map<String, Object> objectMap = (Map<String, Object>) knowledgeBaseRetrieve.execute(null, componentInfo);
            String resultJson = (String) objectMap.get("result");
            if (Objects.isNull(resultJson)) {
                return null;
            }
            return GsonUtil.fromJson(resultJson, KnowledgeBaseRetrieveResult.class);
        } catch (Exception e) {
            log.error("知识库[{}]检索出错, query:{}, queryCondition:{}, e=", knowledgeBase.getName(), query, queryCondition, e);
            throw new RuntimeException("知识库[" + knowledgeBase.getName() + "]检索出错", e);
        }
    }

    private boolean isEmpty(Map<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseIdAndRetrieveResult) {
        if (MapUtils.isEmpty(knowledgeBaseIdAndRetrieveResult)) {
            return true;
        }
        for (Map<String, KnowledgeBaseRetrieveResult> resultMap : knowledgeBaseIdAndRetrieveResult.values()) {
            for (KnowledgeBaseRetrieveResult retrieveResult : resultMap.values()) {
                if (CollectionUtils.isNotEmpty(retrieveResult.getResult())) {
                    return false;
                }
            }
        }
        return true;
    }

    private BotKnowledgeBaseItem generateDefaultConfig(Long knowledgeBaseId) {
        BotKnowledgeBaseItem knowledgeBaseItem = new BotKnowledgeBaseItem();
        knowledgeBaseItem.setKnowledgeBaseId(knowledgeBaseId);
        knowledgeBaseItem.setSimilarityThreshold(0.8);
        knowledgeBaseItem.setTopK(10);

        BotKnowledgeBaseRecallExtConfig recallExtConfig = new BotKnowledgeBaseRecallExtConfig();
        recallExtConfig.setRecallMode(BotRecallModeEnum.TEXT_MODE.getValue());
        knowledgeBaseItem.setRecallExtConfig(recallExtConfig);

        com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeAuthConfig authConfig = new com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeAuthConfig();
        authConfig.setOpen(false);
        knowledgeBaseItem.setAuthConfig(authConfig);

        knowledgeBaseItem.setProcessConfig(Collections.emptyList());
        return knowledgeBaseItem;
    }

}
