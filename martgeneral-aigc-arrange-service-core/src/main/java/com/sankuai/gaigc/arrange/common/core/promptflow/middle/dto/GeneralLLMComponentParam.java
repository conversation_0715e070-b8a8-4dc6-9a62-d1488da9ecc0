/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto;

import com.sankuai.gaigc.arrange.dao.dal.dto.function.FunctionSerializeModel;
import java.util.List;
import lombok.Data;

/**
 * GeneralLLM 组件参数
 *
 * <AUTHOR>
 * @created 2024/12/18
 */
@Data
public class GeneralLLMComponentParam {
    private String appId;
    private String modelName;
    private String userPrompt;
    private List<Object> imgUrls;
    private String systemPrompt;
    private Boolean chatMemoryEnable;
    private Integer maxTokens;
    private Double temperature;
    private Double topP;
    private Double presencePenalty;
    private Double frequencyPenalty;
    private String chatId;
    private Boolean streamOutput;
    private Boolean isJsonFormatOutPut;
    private Boolean isReasoningOutPut;
    private List<FunctionSerializeModel> convertResultFunctions;
    private Integer timeOut;

    private String securityAuditFallbackReply;
    private String llmErrorFallbackReply;
}