---
description: 
globs: 
alwaysApply: true
---
这个项目中的localmethod文件夹内是内置的工具，我现在要做的事情就是将这些工具进行迁移，迁移步骤
1.理解每个工具的代码，判断是否是RPC调用，如果是RPC调用才可以进行转换。要注意识别出是Pigeon的RPC调用还是Thrift的RPC调用
2.将代码转换成脚本语言，如果不能转换说明对应的原因，选择使用DSL还是Java
3.返回的结构要和方法返回的结构相同

DSL脚本语言语法在 /Users/<USER>/Code/martgeneral-aigc-arrange-service/martgeneral-aigc-arrange-service-core/src/main/java/com/sankuai/gaigc/arrange/common/core/promptflow/function/docs/DSL_Grammar.md 转换前你要仔细阅读，必须满足语法规范，不能生成语法不存在的字段，返回完整的DSL脚本

Java脚本语言语法在 /Users/<USER>/Code/martgeneral-aigc-arrange-service/martgeneral-aigc-arrange-service-core/src/main/java/com/sankuai/gaigc/arrange/common/core/promptflow/function/docs/Java_Script_Tutorial.md 转换前你要仔细阅读，必须满足语法规范，不能生成语法不存在的字段，返回内容参考10.1的完整示例