/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.promptflow.middle.service.impl;

import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.component.properties.model.friday.FridayModelProperties;
import com.sankuai.gaigc.arrange.common.core.friday.FuxiChatModel;
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.CompletionTypeParam;
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.GeneralLLMComponentParam;
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.GeneralLLMRequestParam;
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.LLMChatMessageData;
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.service.GeneralLlmMiddleService;
import com.sankuai.gaigc.arrange.dao.dal.dto.function.ToolsSerializeModel;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * Completion类型中间服务
 *
 * <AUTHOR>
 * @created 2024/12/17
 */
@Slf4j
@Component
public class CompletionTypeMiddleService implements GeneralLlmMiddleService {
    private FuxiChatModel mockModel = null;

    @Override
    public AssistantMessage doRequest(GeneralLLMRequestParam requestParam) throws Exception {
        CompletionTypeParam completionTypeParam = (CompletionTypeParam) requestParam;

        GeneralLLMComponentParam componentParam = completionTypeParam.getComponentParam();
        LLMChatMessageData chatMessageData = completionTypeParam.getChatMessageData();
        FridayModelProperties fridayModelProperties = completionTypeParam.getFridayModelProperties();
        FuxiChatModel chatLanguageModel = Objects.isNull(mockModel) ? new FuxiChatModel(componentParam.getAppId(), fridayModelProperties, 
                componentParam.getTimeOut()) : mockModel;

        if (CollectionUtils.isNotEmpty(componentParam.getConvertResultFunctions())) {
            // 转化函数结果
            List<ToolsSerializeModel> sameNameTools = buildSameNameTools(componentParam.getConvertResultFunctions(), chatMessageData.getFuncExecutionResultMessage().getFunctionName());
            return chatLanguageModel.sendMessagesUseCustomFunc(Lists.newArrayList(chatMessageData.getUserMessage(), chatMessageData.getFuncExecutionResultMessage()), sameNameTools);
        }

        return chatLanguageModel.sendVisionMessage(chatMessageData.getChatMessages());
    }
}