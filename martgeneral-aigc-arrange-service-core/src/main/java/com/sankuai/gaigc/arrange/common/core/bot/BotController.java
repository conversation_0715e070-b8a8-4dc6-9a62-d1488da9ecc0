package com.sankuai.gaigc.arrange.common.core.bot;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.gaigc.arrange.api.entity.BotRequest;
import com.sankuai.gaigc.arrange.api.entity.Response;
import com.sankuai.gaigc.arrange.api.entity.UserVO;
import com.sankuai.gaigc.arrange.api.enums.FuxiCallSourceEnum;
import com.sankuai.gaigc.arrange.api.thrift.Param;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.ArenaAbTestService;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.BotAbTestRequest;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.BotAbTestResponse;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.UserModel;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotExecuteService;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotSnapshotService;
import com.sankuai.gaigc.arrange.common.core.monitor.AppExecutionRecorder;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.GrayPublishUtils;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.ResponseUtils;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.ThriftUtils;
import com.sankuai.gaigc.arrange.common.enums.AppEntityTypeEnum;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;


import static com.sankuai.gaigc.arrange.common.core.bot.service.impl.AIBotExecuteServiceImpl.DEFAULT_USER;
import static com.sankuai.gaigc.arrange.api.enums.ErrorCodeEnum.INNER_ERROR;
import static com.sankuai.gaigc.arrange.api.enums.ErrorCodeEnum.INPUT_INVALID;

/**
 * qa测评使用
 *
 * <AUTHOR>
 * @date 2024/7/15 19:07
 */

@Slf4j
@RestController
public class BotController {

    @Autowired
    AIBotExecuteService botExecuteService;

    @Autowired
    AIBotSnapshotService botSnapshotService;

    @Autowired
    AppExecutionRecorder appExecutionRecorder;

    @Autowired
    ArenaAbTestService arenaAbTestService;

    @RequestMapping(path = "/api/bot-run")
    public Response<BotRunResult> runBot(@RequestBody BotRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId()) || request.getId() <= 0) {
            return ResponseUtils.createFailedResponse(INPUT_INVALID, "入参不符合要求");
        }

        if (request.getVersion() == null) {
            //如果正在灰度发布并命中，则替换版本，否则仍为null
            request.setVersion(GrayPublishUtils.getGrayPublishVersion(AppEntityTypeEnum.BOT, request.getId()));
        }

        //执行abTest逻辑 若配置实验 查询分流结果 覆盖版本
        BotAbTestResponse abTestResponse = arenaAbTestService.executeBotAbTest(new BotAbTestRequest(request.getId(), request.getArenaAbRequest()));
        if (abTestResponse.isDoAbTest()) {
            request.setVersion(abTestResponse.getVersion());
        }

        AIBot bot = null;
        BotRunResult runResult = null;
        Exception exception = null;
        BotRunParam runParam = new BotRunParam();
        runParam.setBotId(request.getId());
        runParam.setUserMsg(request.getUserMsg());
        runParam.setUserImgUrls(request.getUserImgUrls());

        String sessionId = StringUtils.isBlank(request.getSessionId()) ? UUID.randomUUID().toString() : request.getSessionId();
        runParam.setSessionId(sessionId);
        UserModel defaultUser = new UserModel();
        BeanUtils.copyProperties(DEFAULT_USER, defaultUser);
        runParam.setUser(Objects.nonNull(request.getUser()) ? convert(request.getUser()) : defaultUser);

        Map<String, Param> inputParams = Maps.newHashMap();
        if (MapUtils.isNotEmpty(request.getInputParams())) {
            for (String key : request.getInputParams().keySet()) {
                Param param = JSONObject.parseObject(request.getInputParams().get(key), Param.class);
                inputParams.put(key, param);
                if ("userInput".equals(key) && StringUtils.isBlank(runParam.getUserMsg())) {
                    runParam.setUserMsg(param.getParamValue());
                }
            }
        }

        if (Objects.nonNull(inputParams)) {
            Map<String, Object> bizParams = inputParams.entrySet().stream().collect(HashMap::new, (hashMap, entry) -> hashMap.put(entry.getKey(), ThriftUtils.parseThriftParam(entry.getValue())), HashMap::putAll);
            runParam.setBizParams(bizParams);
        }

        try {
            runParam.setStream(false);
            StreamInfoHolder.remove();

            bot = botSnapshotService.generateAIBot(runParam.getBotId(), request.getVersion());
            // 执行Bot
            runResult = bot.processQueryByPlan(runParam);
            return ResponseUtils.createSuccessResponse(runResult);
        } catch (Exception e) {
            exception = e;
            log.error("运行Bot出错, request:{}, e=", GsonUtil.toJson(request), e);
            return ResponseUtils.createFailedResponse(INNER_ERROR, e.getMessage());
        } finally {
            //记录日志
            appExecutionRecorder.recordBot(bot, FuxiCallSourceEnum.QA, runResult, runParam, StreamInfoHolder.get() == null ? null : StreamInfoHolder.get().getFirstTokenTime(), exception);
        }

    }

    private UserModel convert(UserVO user) {
        if (user == null) {
            return null;
        }
        UserModel userModel = new UserModel();
        userModel.setUserId(user.getUserId());
        userModel.setUserName(user.getUserName());
        userModel.setUserType(user.getUserType());
        return userModel;
    }
}
