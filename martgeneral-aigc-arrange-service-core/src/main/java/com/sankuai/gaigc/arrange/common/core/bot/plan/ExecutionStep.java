package com.sankuai.gaigc.arrange.common.core.bot.plan;

import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.sankuai.gaigc.arrange.common.constant.ThreadPoolConstants;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.core.bot.StepStatusEnum;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfo;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

/**
 * Agent执行步骤
 */
@Slf4j
@Data
public class ExecutionStep {
    private static final ThreadPool MULTI_PLAN_BOT_TEMPLATE_POOL = ThreadPoolConstants.MULTI_PLAN_BOT_TEMPLATE_POOL;
    private static final long TIME_OUT = 60000;

    private Integer stepNo = -1;
    private StepStatusEnum status = StepStatusEnum.PENDING;
    /**
     * 子步骤
     */
    private List<ExecuteStep> steps;
    /**
     * 子步骤执行结果
     * */
    private List<ExecuteResult> executeResults;


    public ExecutionStep() {
        this.steps = new ArrayList<>();
    }

    public List<ExecuteResult> execute(ExecutePlanContext planContext) {
        return steps.size() == 1 ? singleStepExecute(planContext) : parallelExecute(planContext);
    }
    public List<ExecuteResult> singleStepExecute(ExecutePlanContext planContext) {
        List<ExecuteResult> executeResults = new ArrayList<>();
        ExecuteStep subStep = steps.get(0);
        subStep.setStepNo(1);
        boolean needSkip = Objects.nonNull(subStep.getSkipFunction()) && BooleanUtils.isTrue(subStep.getSkipFunction().apply(planContext));
        if (needSkip) {
            return executeResults;
        }

        subStep.setStatus(StepStatusEnum.RUNNING);
        subStep.setStartTime(System.currentTimeMillis());
        subStep.setSectionId(SSEMessageUtils.generateSectionId());

        ExecuteResult executeResult = subStep.execute(planContext);
        subStep.setStatus(StepStatusEnum.SUCCESS);
        executeResults.add(executeResult);
        subStep.setExecuteResult(executeResult);
        subStep.setSkip(false);
        return executeResults;
    }

    public List<ExecuteResult> parallelExecute(ExecutePlanContext planContext) {
        BotRunParam runParam = planContext.getRunParam();
        boolean availableStream = StreamInfoHolder.availableStream(runParam.getStream());
        StreamInfo streamInfo = StreamInfoHolder.get();

        List<CompletableFuture<ExecuteResult>> futures = Lists.newArrayList();
        AtomicInteger index = new AtomicInteger(1);

        for (ExecuteStep subStep : steps) {
            CompletableFuture<ExecuteResult> future = CompletableFuture.supplyAsync(() -> {
                subStep.setStep(index.getAndIncrement());
                boolean needSkip = Objects.nonNull(subStep.getSkipFunction()) && BooleanUtils.isTrue(subStep.getSkipFunction().apply(planContext));
                if (needSkip) {
                    return null;
                }
                if (availableStream) {
                    StreamInfoHolder.set(streamInfo);
                }
                subStep.setStatus(StepStatusEnum.RUNNING);
                subStep.setStartTime(System.currentTimeMillis());
                subStep.setSectionId(SSEMessageUtils.generateSectionId());
                ExecuteResult execute = subStep.execute(planContext);
                subStep.setStatus(StepStatusEnum.SUCCESS);
                subStep.setExecuteResult(execute);
                subStep.setSkip(false);
                // 清除Rhino线程池中的TL
                StreamInfoHolder.remove();
                return execute;
            }, MULTI_PLAN_BOT_TEMPLATE_POOL.getExecutor());
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        List<ExecuteResult> result = new ArrayList<>();
        try {
            for (CompletableFuture<ExecuteResult> future : futures) {
                ExecuteResult executeResults = future.get(60000, TimeUnit.MILLISECONDS);
                result.add(executeResults);
            }
        } catch (InterruptedException | TimeoutException | ExecutionException e) {
            log.error("Plan步骤并行执行出错, botId:{}", planContext.getBot().getId(), e);
        }

        return result;
    }

}
