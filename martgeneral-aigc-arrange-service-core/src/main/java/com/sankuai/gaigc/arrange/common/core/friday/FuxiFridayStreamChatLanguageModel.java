package com.sankuai.gaigc.arrange.common.core.friday;//

import com.meituan.ai.friday.sdk.api.completion.chat.ChatCompletionRequest;
import com.meituan.ai.friday.sdk.api.completion.chat.ChatMessage;
import com.meituan.ai.friday.sdk.api.completion.chat.ChatMessageRole;
import com.meituan.mdp.langmodel.api.Constants;
import com.meituan.mdp.langmodel.api.function.MdpChatFunction;
import com.meituan.mdp.langmodel.api.message.MdpChatCompletionRequest;
import com.meituan.mdp.langmodel.api.message.MdpChatMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.vision.UserVisionMessage;
import com.meituan.mdp.langmodel.api.model.StreamResponseHandler;
import com.meituan.mdp.langmodel.component.function.MdpFunctionRegistry;
import com.meituan.mdp.langmodel.component.model.chat.stream.FridayStreamChatLanguageModelRequester;
import com.meituan.mdp.langmodel.component.properties.model.friday.FridayModelProperties;
import com.meituan.mdp.langmodel.component.utils.BeanCopyUtils;
import com.meituan.mdp.langmodel.component.utils.ModelHelper;
import com.meituan.mtrace.Tracer;
import com.sankuai.gaigc.arrange.common.util.SerializeUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

@Slf4j
public class FuxiFridayStreamChatLanguageModel{
    private final String fridayAppId;
    private final FridayStreamChatLanguageModelRequester requester;
    List<String> stop = null;
    Map<String, Integer> logitBias = null;
    private FridayModelProperties properties = ModelHelper.defaultChatFridayModelProperties();
    private final Boolean isReasoningPutOut;
    /**
     */
    public FuxiFridayStreamChatLanguageModel(String fridayAppId, FridayStreamChatLanguageModelRequester requester, FridayModelProperties properties, Boolean isReasoningPutOut) {
        this.fridayAppId = fridayAppId;
        this.requester = requester;
        this.properties = properties;
        this.isReasoningPutOut = isReasoningPutOut;
    }

    /**
     * @param msgList
     * @param handler
     * @return
     */
    public StreamAccumulateMessage sendMessages(List<Message> msgList, StreamResponseHandler<StreamAccumulateMessage> handler) {
        //切换到适配imgMessage的方法
        //MdpChatCompletionRequest request = this.generateRequest(msgList);
        MdpChatCompletionRequest request = this.generateImgRequest(msgList);
        if (this.isDebugEnabled()) {
            log.info("FridayChatLanguageModel#sendMessages, msgList={}, request={}", com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(msgList), com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(request));
        }
        return this.httpPost(request, handler);
    }


    public StreamAccumulateMessage sendMessagesUseCustomFunc(List<Message> msgList, List<?> chatFunctionList, StreamResponseHandler<StreamAccumulateMessage> handler) {
        if (CollectionUtils.isEmpty(chatFunctionList)) {
            throw new RuntimeException("Function list is empty");
        }

        FuxiChatCompletionRequest fuxiRequest = generateFuxiRequest(msgList);
        fuxiRequest.setTools(chatFunctionList);
        if (this.isDebugEnabled()) {
            log.info("FridayChatLanguageModel#sendMessagesUseCustomFunc, msgList={}, request={}", com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(msgList), com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(fuxiRequest));
        }
        return this.fuxiHttpPost(fuxiRequest, handler);
    }

    public StreamAccumulateMessage sendMessagesUseFuncNames(List<Message> msgList, List<String> funcNameList, StreamResponseHandler<StreamAccumulateMessage> handler) {
        if (CollectionUtils.isEmpty(funcNameList)) {
            throw new RuntimeException("Function list is empty");
        } else {
            MdpChatCompletionRequest request = this.generateFunctionRequest(msgList, MdpFunctionRegistry.getFunctions(funcNameList));
            if (this.isDebugEnabled()) {
                log.info("FridayChatLanguageModel#sendMessagesUseFuncNames, msgList={}, funcNameList={}, request={}", new Object[]{com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(msgList), com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(funcNameList), com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(request)});
            }

            return this.httpPost(request, handler);
        }
    }

    private StreamAccumulateMessage fuxiHttpPost(FuxiChatCompletionRequest request, StreamResponseHandler<StreamAccumulateMessage> handler) {
        try {
            log.info("FridayChatLanguageModel#fuxiHttpPost, request={}", com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(request));
            return FuxiFridayHttpUtils.doChatStreamingPost(this.fridayAppId, com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(request), handler, this.isReasoningPutOut);
        } catch (Exception var4) {
            throw new RuntimeException(var4);
        }
    }

    private StreamAccumulateMessage httpPost(MdpChatCompletionRequest request, StreamResponseHandler<StreamAccumulateMessage> handler) {
        try {
            log.info("FridayChatLanguageModel#httpPost, request={}", com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(request));
            return FuxiFridayHttpUtils.doChatStreamingPost(this.fridayAppId, com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(request), handler, this.isReasoningPutOut);
        } catch (Exception var4) {
            throw new RuntimeException(var4);
        }
    }

    private boolean isDebugEnabled() {
        return false;
    }

    private MdpChatCompletionRequest generateRequest(List<Message> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            throw new IllegalArgumentException("param msgList can not empty");
        } else {
            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(this.properties.getModel()).messages(ModelHelper.toFridayChatMessages(msgList)).user(Tracer.id()).temperature(this.properties.getTemperature()).topP(this.properties.getTopP()).n(Constants.N).stream(Constants.IS_STREAM).stop(this.stop).maxTokens(this.properties.getMaxTokens()).presencePenalty(this.properties.getPresencePenalty()).frequencyPenalty(this.properties.getFrequencyPenalty()).logitBias(this.logitBias).build();
            return BeanCopyUtils.to(chatCompletionRequest);
        }
    }

    private FuxiChatCompletionRequest generateFuxiRequest(List<Message> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            throw new IllegalArgumentException("generateFuxiRequest param msgList can not empty");
        } else {
            return FuxiChatCompletionRequest.builder()
                    .model(this.properties.getModel())
                    .messages(generateMdpChatMessages(msgList))
                    .user(Tracer.id())
                    .temperature(this.properties.getTemperature())
                    .topP(this.properties.getTopP())
                    .n(Constants.N)
                    .stream(Constants.IS_STREAM).stop(this.stop)
                    .maxTokens(this.properties.getMaxTokens())
                    .presencePenalty(properties.getPresencePenalty())
                    .frequencyPenalty(properties.getFrequencyPenalty())
                    .logitBias(properties.getLogitBias())
                    .build();
        }
    }

    private MdpChatCompletionRequest generateImgRequest(List<Message> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            throw new IllegalArgumentException("param msgList can not empty");
        } else {
            MdpChatCompletionRequest chatCompletionRequest = MdpChatCompletionRequest.builder()
                    .model(this.properties.getModel())
                    .messages(generateMdpChatMessages(msgList))
                    .user(Tracer.id())
                    .temperature(this.properties.getTemperature())
                    .topP(this.properties.getTopP()).n(Constants.N)
                    .stream(Constants.IS_STREAM).stop(this.stop)
                    .maxTokens(this.properties.getMaxTokens())
                    .presencePenalty(this.properties.getPresencePenalty())
                    .frequencyPenalty(this.properties.getFrequencyPenalty())
                    .logitBias(this.logitBias).build();
            return chatCompletionRequest;
        }
    }

    private List<MdpChatMessage> generateMdpChatMessages(List<Message> msgList) {
        List<MdpChatMessage> mdpChatMessages = new ArrayList<>();
        for (Message message : msgList) {
            if (message == null) {
                continue;
            }
            if (message instanceof UserVisionMessage) {
                mdpChatMessages.add(new MdpChatMessage(ChatMessageRole.USER.value(), message.getContent()));
                continue;
            }
            //ChatMessage chatMessage = ModelHelper.toFridayChatMessage(message);
            ChatMessage chatMessage = new ChatMessage(FuxiModelHelper.roleFrom(message).value(), (String) message.getContent(), FuxiModelHelper.nameFrom(message));
            mdpChatMessages.add(toMdpChatMessage(chatMessage));
        }
        return mdpChatMessages;
    }

    private MdpChatMessage toMdpChatMessage(ChatMessage message) {
        MdpChatMessage t = new MdpChatMessage(message.getRole(), message.getContent(), message.getName());
        t.setFunctionCall(BeanCopyUtils.to(message.getFunctionCall()));
        return t;
    }


    private MdpChatCompletionRequest generateFunctionRequest(List<Message> msgList, List<MdpChatFunction> chatFunctionList) {
        if (CollectionUtils.isEmpty(chatFunctionList)) {
            throw new IllegalArgumentException("chatFunctionList can not empty");
        } else {
            MdpChatCompletionRequest chatCompletionRequest = this.generateRequest(msgList);
            chatCompletionRequest.setFunctions(chatFunctionList);
            if (this.isDebugEnabled()) {
                log.info("FridayChatLanguageModel#generateChatCompletionRequest, msgList={}, chatFunctionList={}, chatCompletionRequest={}", new Object[]{com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(msgList), com.sankuai.gaigc.arrange.common.util.SerializeUtils.toJsonStr(chatFunctionList), SerializeUtils.toJsonStr(chatCompletionRequest)});
            }

            return chatCompletionRequest;
        }
    }

    public FridayModelProperties getProperties() {
        return this.properties;
    }

    public static FridayStreamChatLanguageModelBuilder builder() {
        return new FridayStreamChatLanguageModelBuilder();
    }

    public static class FridayStreamChatLanguageModelBuilder {
        private String fridayAppId;
        private FridayStreamChatLanguageModelRequester requester;
        private FridayModelProperties properties;

        FridayStreamChatLanguageModelBuilder() {
        }

        public FridayStreamChatLanguageModelBuilder fridayAppId(final String fridayAppId) {
            this.fridayAppId = fridayAppId;
            return this;
        }

        public FridayStreamChatLanguageModelBuilder requester(final FridayStreamChatLanguageModelRequester requester) {
            this.requester = requester;
            return this;
        }

        public FridayStreamChatLanguageModelBuilder properties(final FridayModelProperties properties) {
            this.properties = properties;
            return this;
        }
        public String toString() {
            return "FridayStreamChatLanguageModel.FridayStreamChatLanguageModelBuilder(fridayAppId=" + this.fridayAppId + ", requester=" + this.requester + ", properties=" + this.properties + ")";
        }
    }
}
