package com.sankuai.gaigc.arrange.common.core.friday.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.ai.friday.sdk.api.completion.chat.ChatMessageRole;
import lombok.*;

import java.util.List;

/**
 * @ClassName ChatMessage
 * <AUTHOR>
 * @Date 2025/2/19 下午3:57
 */
@Data
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class ChatMessage {

    /**
     * Must be either 'system', 'user', 'assistant' or 'function'.<br>
     * You may use {@link ChatMessageRole} enum.
     */
    @NonNull
    String role;
    @JsonInclude() // content should always exist in the call, even if it is null
    String content;
    //name is optional, The name of the author of this message. May contain a-z, A-Z, 0-9, and underscores, with a maximum length of 64 characters.
    String name;
    @JsonProperty("function_call")
    ChatFunctionCall functionCall;

    @JsonProperty("tool_calls")
    private List<ToolCall> toolCalls;

    @JsonProperty("reasoning_content")
    private String reasoningContent;

    @Data
    public static class ToolCall {
        private String id;
        private String type;
        private ChatFunctionCall function;
    }

    public ChatMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }

    public ChatMessage(String role, String content, String name) {
        this.role = role;
        this.content = content;
        this.name = name;
    }

}