package com.sankuai.gaigc.arrange.common.core.promptflow.util;

import org.apache.commons.lang3.StringUtils;
import py4j.Py4JException;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.UndeclaredThrowableException;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 异常解析util
 * 张俊杰
 * 2024年09月09日10:39:01
 */
public class ExceptionParseUtil {
    //提取出最后一次出现 File "<string>", 后面的所有内容。
    private static final String regex = ".*File \"<string>\",(.*)$";
    // Pattern.DOTALL模式让"."匹配包括行终止符在内的所有字符
    private static final Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);

    /**
     * 解析Python异常
     *
     * @return
     */
    public static Exception parsePythonException(Exception e, String pythonScriptName) {
        if (e instanceof UndeclaredThrowableException) {
            UndeclaredThrowableException undeclaredThrowableException = (UndeclaredThrowableException) e;
            if (undeclaredThrowableException.getUndeclaredThrowable() instanceof InvocationTargetException) {
                InvocationTargetException invocationTargetExceptionTemp = (InvocationTargetException) undeclaredThrowableException.getUndeclaredThrowable();
                if ((invocationTargetExceptionTemp.getTargetException() instanceof Py4JException)) {
                    Py4JException py4jException = (Py4JException) invocationTargetExceptionTemp.getTargetException();
                    String pythonErrorMessage = parsePythonExceptionMessage(py4jException);
                    //如果解析出错误信息才抛异常,否则维持原样
                    if (StringUtils.isNotBlank(pythonErrorMessage)) {
                        String prefix = "脚本 \"" + pythonScriptName + "\" 执行异常: ";
                        return new Py4JException(prefix + pythonErrorMessage);
                    }
                }
            }
        }
        return e;
    }

    /**
     * 解析处理Python异常消息
     *
     * @param py4jException
     * @return
     */
    public static String parsePythonExceptionMessage(Py4JException py4jException) {
        String errorMessage = Optional.ofNullable(py4jException)
                .map(Throwable::getMessage)
                .orElse("");
        if (org.apache.commons.lang3.StringUtils.isBlank(errorMessage)) {
            return errorMessage;
        }
        Matcher matcher = pattern.matcher(errorMessage);
        if (matcher.find()) {
            // 获取File "<string>"后面所有的内容
            return matcher.group(1);
        }
        return "";
    }
}
