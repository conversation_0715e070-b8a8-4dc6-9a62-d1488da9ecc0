package com.sankuai.gaigc.arrange.common.core.promptflow.function.remotemethod;

import com.meituan.lvyou.mermaid.message.shelf.model.info.PoiNotice;
import com.meituan.lvyou.mermaid.message.thrift.IMermaidThriftService;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/8/9 16:37
 */


@Service
public class MermaidService {

    @MdpThriftClient(remoteAppKey = "com.meituan.lvyou.mermaid.server",timeout = 1000,remoteServerPort = 8419)
    IMermaidThriftService mermaidThriftService;

    private Set<Integer> displaySides = new HashSet<>(Arrays.asList(0,1));

    public PoiNotice getNoticeByPoiId(Long poiId){
        Map<Long, PoiNotice> poiNotice = mermaidThriftService.getPoiNoticeV4(Arrays.asList(poiId), displaySides);
        return poiNotice.get(poiId);
    }
}
