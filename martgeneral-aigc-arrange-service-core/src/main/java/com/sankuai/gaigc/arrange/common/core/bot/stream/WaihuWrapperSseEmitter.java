package com.sankuai.gaigc.arrange.common.core.bot.stream;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.gaigc.arrange.api.entity.waihu.WaiHuBotResponse;
import com.sankuai.gaigc.arrange.api.entity.waihu.WaiHuBotResponseData;
import com.sankuai.gaigc.arrange.api.entity.waihu.WaiHuMsgContent;
import com.sankuai.gaigc.arrange.api.entity.waihu.WaiHuTtsConfig;
import com.sankuai.gaigc.arrange.api.enums.WaiHuResponseCommandEnum;
import com.sankuai.gaigc.arrange.common.constant.MessageTypeConstants;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.constants.AIBotRoleConstants;
import com.sankuai.gaigc.arrange.common.core.bot.constants.WaiHuApiConstants;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/2/19 19:36
 * @desc
 */
@Slf4j
public class WaihuWrapperSseEmitter extends SseEmitter {
    // 定义断句符号的正则表达式
    private static final String BREAK_SYMBOLS_REGEX = "[。！？；,.;!?]";
    private static final Pattern BREAK_PATTERN = Pattern.compile(BREAK_SYMBOLS_REGEX);
    private final Map<String, StringBuilder> sessionMsgContent = Maps.newConcurrentMap();
    private String turnId; // 轮次id
    private WaiHuTtsConfig whTtsConfig; // tts配置

    public static WaihuWrapperSseEmitter wrap(Long timeout, String turnId, WaiHuTtsConfig whTtsConfig) {
        WaihuWrapperSseEmitter waihuEmitter = new WaihuWrapperSseEmitter(timeout);
        waihuEmitter.turnId = turnId;
        if (Objects.isNull(whTtsConfig)) {
            whTtsConfig = new WaiHuTtsConfig();
        }
        waihuEmitter.whTtsConfig = whTtsConfig;
        return waihuEmitter;
    }


    /**
     * Create a SseEmitter with a custom timeout value.
     * <p>By default not set in which case the default configured in the MVC
     * Java Config or the MVC namespace is used, or if that's not set, then the
     * timeout depends on the default of the underlying server.
     *
     * @param timeout timeout value in milliseconds
     * @since 4.2.2
     */
    private WaihuWrapperSseEmitter(Long timeout) {
        super(timeout);
    }


    /**
     * Send the object formatted as a single SSE "data" line. It's equivalent to:
     * <pre>
     * // static import of SseEmitter.*
     *
     * SseEmitter emitter = new SseEmitter();
     * emitter.send(event().data(myObject));
     * </pre>
     *
     * <p>Please, see {@link ResponseBodyEmitter#send(Object) parent Javadoc}
     * for important notes on exception handling.
     *
     * @param object the object to write
     * @throws IOException           raised when an I/O error occurs
     * @throws IllegalStateException wraps any other errors
     */
    @Override
    public void send(Object object) throws IOException {
        if (!(object instanceof SSEMessage)) {
            return;
        }
        SSEMessage sseMessage = (SSEMessage) object;
        if (isFilterMessage(sseMessage)) {
            return;
        }

        if (processFinalMessage(sseMessage)) {
            return;
        }

        if (StringUtils.isBlank(sseMessage.getContent())) {
            return;
        }
        String content = processMessageContent(sseMessage);
        if (StringUtils.isBlank(content)) {
            return;
        }

        sendResponse(sseMessage, content);
    }

    /**
     * 是否为需要过滤的消息
     */
    private boolean isFilterMessage(SSEMessage sseMessage) {
        return !sseMessage.getRole().equals(AIBotRoleConstants.ASSISTANT)
                || !sseMessage.getType().equals(MessageTypeConstants.ANSWER)
                || Objects.isNull(sseMessage.getIndex())
                || sseMessage.getIndex() < 0;
    }

    /**
     * 处理单次回复完成的消息
     */
    private boolean processFinalMessage(SSEMessage sseMessage) throws IOException {
        if (!sseMessage.getIs_finish() || !sessionMsgContent.containsKey(sseMessage.getSession_id())) {
            return false;
        }
        // 一个回复完成
        String msg = sessionMsgContent.get(sseMessage.getSession_id()).toString();
        sendResponse(sseMessage, msg);
        return true;
    }

    /**
     * 处理断句后的消息
     */
    private String processMessageContent(SSEMessage sseMessage) {
        sessionMsgContent.putIfAbsent(sseMessage.getSession_id(), new StringBuilder());
        String content = null;

        for (char c : sseMessage.getContent().toCharArray()) {
            sessionMsgContent.get(sseMessage.getSession_id()).append(c);
            // 按照标点断句
            if (isBreakSymbols(c)) {
                content = sessionMsgContent.get(sseMessage.getSession_id()).toString();
                sessionMsgContent.put(sseMessage.getSession_id(), new StringBuilder());
            }
        }

        return content;
    }

    private void sendResponse(SSEMessage sseMessage, String content) throws IOException {
        List<WaiHuMsgContent> waiHuMsgContents = Lists.newArrayList();
        if (StringUtils.isNotBlank(content)) {
            waiHuMsgContents.add(new WaiHuMsgContent(content, WaiHuApiConstants.WH_MSG_TEXT_TYPE));
        }
        WaiHuBotResponseData response = createResponse(sseMessage.getSession_id(), waiHuMsgContents, sseMessage.getIs_finish());

        // 是否包含挂断电话标识
        if (containHangUpKeywords(content)) {
            content = replaceHangUpKeywords(content);
            waiHuMsgContents.clear();
            response.setContents(waiHuMsgContents);
            response.setFinish(true);
            waiHuMsgContents.add(new WaiHuMsgContent(content, WaiHuApiConstants.WH_MSG_TEXT_TYPE));
            send(response);

            waiHuMsgContents.clear();
            waiHuMsgContents.add(new WaiHuMsgContent(WaiHuResponseCommandEnum.HANG_UP.getCommand(), WaiHuApiConstants.WH_MSG_COMMAND_TYPE));
            sessionMsgContent.remove(sseMessage.getSession_id());
        } else if (sseMessage.getIs_finish()) {
            response.setFinish(true);
            sessionMsgContent.remove(sseMessage.getSession_id());
        }

        send(response);
    }

    private void send(WaiHuBotResponseData responseData) throws IOException {
        log.info("WaihuWrapperSseEmitter send response:{}", JSON.toJSONString(responseData));
        super.send(WaiHuBotResponse.success(responseData));
    }

    private WaiHuBotResponseData createResponse(String sessionId, List<WaiHuMsgContent> contents, boolean isFinish) {
        WaiHuBotResponseData response = new WaiHuBotResponseData();
        response.setTurnId(this.turnId);
        response.setConversationId(sessionId);
        response.setContents(contents);
        response.setFinish(isFinish);
        response.setTtsConfig(this.whTtsConfig);
        return response;
    }

    private boolean isBreakSymbols(char input) {
        String str = String.valueOf(input);
        return BREAK_PATTERN.matcher(str).matches();
    }

    private boolean containHangUpKeywords(String content) {
        if (StringUtils.isBlank(content)) {
            return false;
        }
        for (String keyword : WaiHuApiConstants.WH_ACTIVE_HANG_UP_KEYWORDS) {
            if (content.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    private String replaceHangUpKeywords(String content) {
        for (String keyword : WaiHuApiConstants.WH_ACTIVE_HANG_UP_KEYWORDS) {
            if (content.contains(keyword)) {
                return content.replace(keyword, SymbolConstant.EMPTY);
            }
        }
        return content;
    }

    public static String handlePrologue(String prologue) {
        char lastChar = prologue.charAt(prologue.length() - 1);
        if (BREAK_PATTERN.matcher(String.valueOf(lastChar)).matches()) {
            return prologue;
        }
        return prologue + SymbolConstant.PERIOD;
    }
}
