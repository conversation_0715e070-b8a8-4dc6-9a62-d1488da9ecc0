/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig;

import lombok.Data;

/**
 * Bot变量
 *
 * <AUTHOR>
 * @created 2024/6/26
 */
@Data
public class BotVariableItem {
    /**
     * 字段: name
     * 说明: 变量名称
     */
    private String name;

    /**
     * 字段: type
     * 说明: 变量类型，0：布尔，1：byte，2：short等，与ParamType保持一致
     */
    private Integer type;

    /**
     * 字段: value
     * 说明: 变量值
     */
    private String value;

    /**
     * 字段: description
     * 说明: 变量描述
     */
    private String description;
}
