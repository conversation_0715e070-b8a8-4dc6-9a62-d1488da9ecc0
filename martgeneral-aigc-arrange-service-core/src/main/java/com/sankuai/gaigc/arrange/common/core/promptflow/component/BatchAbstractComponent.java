package com.sankuai.gaigc.arrange.common.core.promptflow.component;

import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.ComponentNameEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.CatLogUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @created: 2024/4/22
 * @description:
 */
@Slf4j(topic = "scribe")
public class BatchAbstractComponent extends AbstractComponent {
    protected Boolean batchProcessOption;
    protected List<Object> batchProcessItem;

    private static final Boolean BATCH_DEFAULT_OPTION = false;

    @Override
    public void onStart(Context context, ComponentInfo componentInfo) throws Exception{
        // 埋点
        CatLogUtils.logComponent(componentInfo);
        batchProcessOption = Optional.ofNullable((Boolean) parseParam(ComponentNameEnum.BATCH_PROCESS_OPTION.getName(), componentInfo)).
                orElse(BATCH_DEFAULT_OPTION);
        if (batchProcessOption) {
            batchProcessItem = (List<Object>) parseParam(ComponentNameEnum.BATCH_PROCESS_ITEM.getName(), componentInfo);
        }
        super.onStart(context, componentInfo);
    }
}
