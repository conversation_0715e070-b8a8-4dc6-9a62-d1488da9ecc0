/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.service;

import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeAuthConfig;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.UserModel;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult;
import java.util.Map;

/**
 * 知识鉴权服务
 *
 * <AUTHOR>
 * @created 2024/5/31
 */
public interface BotKnowledgeAuthService {

    Map<String, KnowledgeBaseRetrieveResult> authData(Map<String, KnowledgeBaseRetrieveResult> retrieveResult, UserModel user, BotKnowledgeAuthConfig authConfig);

}
