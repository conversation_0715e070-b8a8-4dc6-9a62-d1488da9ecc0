package com.sankuai.gaigc.arrange.common.core.promptflow.function;

import java.util.Map;

public class FunctionCallNotExecuteResult {
    //要执行的工具名
    private String name;
    //工具执行参数
    private Map<String,Object> arguments;
    //意图（大模型识别模式下等于name）
    private String intent;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Object> getArguments() {
        return arguments;
    }

    public void setArguments(Map<String, Object> arguments) {
        this.arguments = arguments;
    }

    public String getIntent() {
        return intent;
    }

    public void setIntent(String intent) {
        this.intent = intent;
    }

    @Override
    public String toString() {
        return "FunctionCallNotExecuteResult{" +
                "name='" + name + '\'' +
                ", arguments=" + arguments +
                '}';
    }
}
