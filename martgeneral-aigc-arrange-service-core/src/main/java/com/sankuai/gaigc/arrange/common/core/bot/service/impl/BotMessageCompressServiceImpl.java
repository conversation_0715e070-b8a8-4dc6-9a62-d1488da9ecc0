package com.sankuai.gaigc.arrange.common.core.bot.service.impl;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.store.memory.ChatMemoryStore;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.gaigc.arrange.api.entity.Response;
import com.sankuai.gaigc.arrange.api.service.PromptFlowExecutionService;
import com.sankuai.gaigc.arrange.common.core.bot.service.BotMessageCompressService;
import com.sankuai.gaigc.arrange.common.core.bot.utils.HistoryMessageUtils;
import com.sankuai.gaigc.arrange.common.core.friday.FuxiChatMemory;
import com.sankuai.gaigc.arrange.config.MccConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@Service
@Slf4j
public class BotMessageCompressServiceImpl implements BotMessageCompressService {
    @Autowired
    private PromptFlowExecutionService promptFlowExecutionService;

    @Autowired
    private MccConfig mccConfig;

    @Autowired
    @Qualifier("squirrelChatMemoryStore")
    private ChatMemoryStore squirrelChatMemoryStore;

    public static ExecutorService MESSAGE_COMPRESS_EXECUTOR =  TraceExecutors.getTraceExecutorService(
            Rhino.newThreadPool("message_compress_executorn-pool",
                            DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(100).withMaxQueueSize(2000)
                    )
                    .getExecutor());

    @Override
    public void messageCompress(List<Message> messages, String memoryId) {
        List<Message> messageList = new ArrayList<>(messages);

        CompletableFuture.runAsync(() -> {
            try {
                String chatMessage = HistoryMessageUtils.assembleHisttoryMessage(messageList);
                Long flowId = mccConfig.getChatMessageCompressFlowId();
                Map<String, Object> inputs = new HashMap<>();
                inputs.put("chatMessage", chatMessage);
                Response<Map<String, Object>> mapResponse = promptFlowExecutionService.runFlow(flowId, inputs);
                Map<String, Object> data = mapResponse.getData();
                String absRes = data.get("abstract").toString();

                FuxiChatMemory fuxiChatMemory = new FuxiChatMemory(memoryId, squirrelChatMemoryStore);
                List<Message> messages1 = fuxiChatMemory.messages();
                if (CollectionUtils.isNotEmpty(messages1)) {
                    fuxiChatMemory.clear();
                }
                fuxiChatMemory.add(new AssistantMessage(absRes));
            } catch (Exception e) {
                log.error("compress chat message error!" + memoryId, e);
            }
        }, MESSAGE_COMPRESS_EXECUTOR);
    }
}
