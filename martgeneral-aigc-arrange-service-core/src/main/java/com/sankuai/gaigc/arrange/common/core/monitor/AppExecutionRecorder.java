package com.sankuai.gaigc.arrange.common.core.monitor;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mtrace.Tracer;
import com.meituan.mdp.langmodel.api.message.MdpUsage;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.gaigc.arrange.api.enums.FuxiCallSourceEnum;
import com.sankuai.gaigc.arrange.api.enums.AppExecutionStatusEnum;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.promptflow.dsl.Flow;
import com.sankuai.gaigc.arrange.common.core.promptflow.dsl.Node;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentParam;
import com.sankuai.gaigc.arrange.common.core.taskflow.DagEngine;
import com.sankuai.gaigc.arrange.common.core.taskflow.operator.OperatorResult;
import com.sankuai.gaigc.arrange.common.util.MaskSecretUtils;
import com.sankuai.gaigc.arrange.dao.dal.dto.function.AigcFunctionDto;
import com.sankuai.gaigc.arrange.dao.dalblade.entity.FlowComponentLogExtDO;
import com.sankuai.gaigc.arrange.dao.dalblade.mapper.FlowComponentLogExtMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import com.sankuai.gaigc.arrange.common.constant.TracerConstants;
import com.google.gson.Gson;

/**
 * 应用执行记录器
 */
@Slf4j
@Service
public class AppExecutionRecorder {
    private static final ExecutorService componentLogRecordExecutor = TraceExecutors.getTraceExecutorService(
            Rhino.newThreadPool("AppExecutionRecorder-execution-pool",
                    DefaultThreadPoolProperties.Setter()
                            .withCoreSize(50)
                            .withMaxSize(100)
                            .withMaxQueueSize(1000)
            ).getExecutor());

    private static final List<String> LLM_NODE_NAME_LIST = Arrays.asList("GeneralLLM", "FunctionCalling", "BaiduErnieLLM", "meituanLLM");
    public static final int MBytes = 1048576 / 2;
    private static final int MaxLen = 20480;

    @Autowired
    @Qualifier("flowExecutionLogProducer")
    private IProducerProcessor flowExecutionLogProducer;

    @Autowired
    @Qualifier("botExecutionLogProducer")
    private IProducerProcessor botExecutionLogProducer;

    @Autowired
    @Qualifier("funcExecutionLogProducer")
    private IProducerProcessor funcExecutionLogProducer;


    @Autowired
    private FlowComponentLogExtMapper flowComponentLogExtMapper;

    private static final Gson GSON = new Gson();


    /**
     * 记录工作流执行情况，web服务监听消息，记录进行日志并生成报表
     */
    public void recordFlow(Flow flow, DagEngine<Map<String, Object>> engine, Long startTime, Map<String, Object> inputParams) {
        String msg = null;
        try {
            Map<String, Object> output = (engine.getDagContext() != null && engine.getDagContext().getOutput() != null) ? engine.getDagContext().getOutput().getResult() : Maps.newHashMap();
            Map<String, OperatorResult> operatorResultMap = (engine.getDagContext() != null) ? engine.getDagContext().getOperatorResultMap() : Maps.newHashMap();
            FlowExecutionLogMessage executionLogMessage = buildFlowLogMsg(flow, engine.getEx(), startTime, inputParams, output, operatorResultMap);
            msg = GSON.toJson(executionLogMessage);
            //log.info("发送工作流执行日志MQ" + msg);
            flowExecutionLogProducer.sendMessage(msg);
        } catch (Exception e) {
            log.error("发送工作流执行日志MQ时发生异常:" + msg, e);
        }
    }

    /**
     * 记录bot执行情况，web服务监听消息，记录进行日志并生成报表
     */
    public void recordBot(AIBot bot, FuxiCallSourceEnum callSource, BotRunResult botRunResult, BotRunParam botRunParam, Long firstTokenTime, Exception exception) {
        String msg = null;
        try {
            msg = buildBotLogMessage(bot, callSource, botRunResult, botRunParam, firstTokenTime, exception);
            //log.info("发送bot执行日志MQ" + msg);
            botExecutionLogProducer.sendMessage(msg);
        } catch (Exception e) {
            log.error("发送bot日志MQ时发生异常:" + msg, e);
        }
    }

    public void recordFunction(Long startTime, ComponentInfo componentInfo, AigcFunctionDto functionDto, boolean isSuccess) {
        String msg = null;
        try {
            FunctionExecutionLogMessage functionExecutionLogMessage = FunctionExecutionLogMessage.builder()
                    .funcId(functionDto.getId())
                    .funcName(functionDto.getName())
                    .funcType(functionDto.getFuncType().getValue())
                    .executionStatus(isSuccess ? AppExecutionStatusEnum.SUCCESS.getType() : AppExecutionStatusEnum.EXCEPTION.getType())
                    .startTime(startTime)
                    .endTime(System.currentTimeMillis())
                    .duration(System.currentTimeMillis() - startTime)
                    .creator(functionDto.getCreator())
                    .traceId(Tracer.id())
                    .flowId(componentInfo.getFlowId() == null ? null : Long.valueOf(componentInfo.getFlowId()))
                    .appId(componentInfo.getAppId() == null ? null :Long.valueOf(componentInfo.getAppId()))
                    .botId(componentInfo.getBotId() == null ? null :Long.valueOf(componentInfo.getBotId()))
                    .sectionId(componentInfo.getSectionId() == null ? null :Long.valueOf(componentInfo.getSectionId()))
                    .build();
            msg = GSON.toJson(functionExecutionLogMessage);
            funcExecutionLogProducer.sendMessage(msg);
        } catch (Exception e) {
            log.error("发送插件function日志MQ时发生异常:" + msg, e);
        }
    }

    private FlowExecutionLogMessage buildFlowLogMsg(Flow flow, Throwable ex, Long startTime, Map<String, Object> inputParams, Map<String, Object> output, Map<String, OperatorResult> operatorResultMap) {
        if(flow == null) {
            throw new IllegalArgumentException("flow is null");
        }
        FlowExecutionLogMessage executionLogMessage = new FlowExecutionLogMessage();
        executionLogMessage.setAppId(flow.getAppId());
        executionLogMessage.setFlowId(flow.getId());
        executionLogMessage.setFlowName(flow.getName());
        executionLogMessage.setExecutionStatus(ex == null? AppExecutionStatusEnum.SUCCESS.getType() : AppExecutionStatusEnum.EXCEPTION.getType());
        executionLogMessage.setStartTime(startTime);
        executionLogMessage.setEndTime(System.currentTimeMillis());
        executionLogMessage.setInput(getTruncatedIO(inputParams));
        executionLogMessage.setOutput(extractOutput(ex, output));
        FuxiCallSourceEnum callSource = FuxiCallSourceEnum.findByName(Tracer.getContext(TracerConstants.FLOW_CALL_SOURCE));
        executionLogMessage.setCallSource(callSource != null ? callSource.getType() : FuxiCallSourceEnum.API.getType());//默认api来源
        executionLogMessage.setTraceId(Tracer.id());
        extractInfo(flow, operatorResultMap, executionLogMessage);
        return executionLogMessage;
    }

    private Map<String,Object> extractOutput(Throwable ex, Map<String, Object> output) {
        if(ex != null) {
            Map<String,Object> errorDetail = Maps.newHashMap();
            errorDetail.put("errorMessage", ex.getMessage());
            return errorDetail;
        } 
        // mafka有限制，消息太大会发送失败，进行截断
        return getTruncatedIO(output);
    }

    private Map<String,Object> getTruncatedIO(Map<String,Object> ioMap) {
        String ioStr = GSON.toJson(ioMap);
        if (ioStr.length() > MBytes) {
            Map<String, Object> truncatedMap = Maps.newHashMap();
            truncatedMap.put("WARN", "输入输出长度超出限制，可通过日志树查看");
            return truncatedMap;
        }
        return ioMap;
    }

    /**
     * 构建Bot执行日志消息
     *
     * @param callSource 调用来源枚举
     * @param firstTokenTime 首token生成时间
     */
    private String buildBotLogMessage(AIBot bot, FuxiCallSourceEnum callSource, BotRunResult botRunResult, BotRunParam botRunParam, Long firstTokenTime, Exception exception) {
        if(bot == null || botRunParam == null) {
            throw new IllegalArgumentException("bot or botRunResult or botRunParam is null");
        }
        BotRunResult runResultTmp = botRunResult != null ? botRunResult : new BotRunResult(false, null, "Bot执行出错:" + exception);
        BotExecutionLogMessage botExecutionLogMessage = BotExecutionLogMessage.builder()
                .botId(bot.getId())
                .appId(bot.getAppId())
                .botName(bot.getName())
                .executionStatus(runResultTmp.isSuccess() ? AppExecutionStatusEnum.SUCCESS.getType() : AppExecutionStatusEnum.EXCEPTION.getType())
                .startTime(bot.getStart())
                .endTime(System.currentTimeMillis())
                .userMsg(botRunParam.getUserMsg())
                .bizParams(botRunParam.getBizParams())
                .result(runResultTmp.getResult())
                .replyContent(runResultTmp.isSuccess() ? runResultTmp.getReplyContent() : runResultTmp.getMessage())
                .callSource(callSource.getType())
                .botSource(bot.getBotSource().getValue())
                .traceId(Tracer.id())
                .version(Integer.valueOf(bot.getVersion()))
                .firstTokenTime(firstTokenTime)
                .build();
        return GSON.toJson(botExecutionLogMessage);
    }

    /**
     * 提取组件、tokens、租户ID信息
     */
    private void extractInfo(Flow flow, Map<String, OperatorResult> operatorResultMap, FlowExecutionLogMessage flowExecutionLogMessage) {
        long overallToken = 0;
        List<ComponentNodeInfo> componentNodeInfoList = Lists.newArrayList();
        for(Node node : flow.getNodeInstances()) {
            //构建组件信息
            ComponentNodeInfo componentNodeInfo = buildComponentNodeInfo(node, operatorResultMap, flowExecutionLogMessage);
            componentNodeInfoList.add(componentNodeInfo);
            if(LLM_NODE_NAME_LIST.contains(node.getComponentName())) {
                setTenantId(node, flowExecutionLogMessage);
                overallToken += componentNodeInfo.getToken();
            }
        }
        flowExecutionLogMessage.setTokens(overallToken);
        flowExecutionLogMessage.setComponentNodesInfo(componentNodeInfoList);
    }

    /**
     * 根据节点和操作结果映射，创建并填充组件节点信息对象。
     *
     * @param operatorResultMap 包含节点执行结果的映射，键为节点实例名称，值为操作结果。
     */
    protected ComponentNodeInfo buildComponentNodeInfo(Node node, Map<String, OperatorResult> operatorResultMap, FlowExecutionLogMessage flowExecutionLogMessage) {
        ComponentNodeInfo componentNodeInfo = ComponentNodeInfo.builder()
                .componentName(node.getInstanceName())
                .componentType(node.getComponentName())
                .build();
        operatorResultMap.entrySet().stream()
                .filter(entry -> node.getInstanceName().equals(entry.getKey()))
                .findFirst()
                .ifPresent(entry -> {
                    OperatorResult value = entry.getValue();
                    //解析出tokenUsage
                    MdpUsage usage = JSON.parseObject((String) ((Map<?, ?>)value.getResult()).get("usage"), MdpUsage.class);
                    componentNodeInfo.setDuration(value.getDuration());
                    componentNodeInfo.setResultState(value.getResultState().name());
                    componentNodeInfo.setToken(usage == null ? 0 : (int) usage.getTotalTokens());
                    recordComponentIO(node.getInputParam(), (Map<String, Object>) value.getResult(), componentNodeInfo, flowExecutionLogMessage);
                });
        return componentNodeInfo;
    }

    /**
     * 大模型组件-计算token并获取租户ID
     */
    protected void setTenantId(Node node, FlowExecutionLogMessage flowExecutionLogMessage) {
        Map<String, Object> inputMap = node.getInputParam().stream()
                .filter(param -> param.getName() != null && param.getValue() != null)
                .collect(Collectors.toMap(ComponentParam::getName, ComponentParam::getValue));
        if (inputMap.containsKey("appId")) {
            flowExecutionLogMessage.setTenantId(MaskSecretUtils.maskFridayAppId((String)inputMap.get("appId")));
        }
    }


    /**
     * 检查组件输入输出，如果内容过大，则异步直接写入blade表
     */
    private void recordComponentIO(List<ComponentParam> inputParam, Map<String, Object> result, ComponentNodeInfo componentNodeInfo, FlowExecutionLogMessage flowExecutionLogMessage)  {
        //把输入输出转换成str计算大小
        String inputStr = GSON.toJson(inputParam);
        String outputStr = GSON.toJson(result);

        Map<String, Object> inputMap = Maps.newHashMap();
        inputParam.forEach(param -> inputMap.put(param.getName(), param.getValue()));

        if (inputStr.length() + outputStr.length() > MaxLen) {
            //异步写blade
            componentLogRecordExecutor.execute(() -> writeComponentLog2Blade(inputMap, result, componentNodeInfo, flowExecutionLogMessage));
        } else {
            componentNodeInfo.setInput(inputMap);
            componentNodeInfo.setOutput(result);
        }

    }

    private void writeComponentLog2Blade(Map<String, Object> inputParam, Map<String, Object> result, ComponentNodeInfo componentNodeInfo, FlowExecutionLogMessage flowExecutionLogMessage) {
        FlowComponentLogExtDO flowComponentLogExt = FlowComponentLogExtDO.builder()
                .appId(Long.valueOf(flowExecutionLogMessage.getAppId()))
                .flowId(Long.valueOf(flowExecutionLogMessage.getFlowId()))
                .traceId(flowExecutionLogMessage.getTraceId())
                .componentName(componentNodeInfo.getComponentName())
                .startTime(flowExecutionLogMessage.getStartTime())
                .createTime(System.currentTimeMillis())
                .input(GSON.toJson(inputParam))
                .output(GSON.toJson(result))
                .build();
        flowComponentLogExtMapper.insert(flowComponentLogExt);
    }


    /**
     * 工作流执行日志消息类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FlowExecutionLogMessage implements Serializable {
        private String appId;
        private String flowId;
        private String flowName;
        private String tenantId;
        private int executionStatus;
        private Long startTime; //毫秒
        private Long endTime;
        private Map<String, Object> input;
        private Map<String, Object> output;
        private Long tokens;
        private Integer callSource;//调用来源
        private String traceId;
        private List<ComponentNodeInfo> componentNodesInfo;
    }

    /**
     * 机器人执行日志消息类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BotExecutionLogMessage implements Serializable {
        private Long botId;
        private Long appId;
        private String botName;
        private int executionStatus;
        private Long startTime;
        private Long endTime;
        private String userMsg;/** 用户提问内容 */
        private Map<String, Object> bizParams;/** 业务参数 */
        private Map<String, Object> result; /** 非标准化结果，如工作流 */
        private String replyContent;//回复内容
        private Integer callSource;//调用来源
        private Integer botSource;//com.sankuai.gaigc.arrange.common.core.bot.enums.BotSourceEnum;
        private String traceId;
        private Integer version;
        private Long firstTokenTime;
    }

    /**
     * 插件工具执行日志消息类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class FunctionExecutionLogMessage implements Serializable {
        private Long funcId;
        private String funcName;
        private int funcType;
        private int executionStatus;
        private Long startTime;
        private Long endTime;
        private Long duration;
        private String creator;
        private String traceId;
        private Long flowId;
        private Long appId;
        private Long botId;
        private Long sectionId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ComponentNodeInfo implements Serializable {
        private Long duration;
        private String componentName;
        private String componentType;
        private String resultState; //com.sankuai.gaigc.arrange.common.core.taskflow.enums.ResultState
        private int token;
        Map<String, Object> input;
        Map<String, Object> output;
    }
}
