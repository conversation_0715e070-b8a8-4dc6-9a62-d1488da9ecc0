package com.sankuai.gaigc.arrange.common.core.monitor;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.gaigc.arrange.api.enums.FlowRunModeEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.engine.PromptFlowEngine;

import java.util.Objects;

/**
 * 张俊杰 2024年06月21日14:54:21
 */
@SuppressWarnings("JavadocReference")
public class FlowTransactionManager {

    private static final String CLASS_MARK = PromptFlowEngine.class.getSimpleName();

    public static String getFlowTransactionClassMarkName() {
        return CLASS_MARK;
    }

    /**
     * unit test see{@link com.sankuai.gaigc.arrange.promptflow.transaction.FlowTransactionManagerSpec#createTransactionTest()}
     */
    public static Transaction createTransaction(FlowRunModeEnum flowRunModeEnum, String name) {
        //batch单独一个
        //debug单独一个
        if (Objects.equals(flowRunModeEnum, FlowRunModeEnum.BATCH) ||
                Objects.equals(flowRunModeEnum, FlowRunModeEnum.DEBUG)) {
            return createRunFlowTransaction(flowRunModeEnum.name(), name);
        }
        // 使用默认的....
        return createDefaultRunFlowTransaction(name);
    }


    private static Transaction createDefaultRunFlowTransaction(String name) {
        return Cat.newTransaction(CLASS_MARK, name);
    }

    private static Transaction createRunFlowTransaction(String type, String name) {
        return Cat.newTransaction(CLASS_MARK + "_" + type, name);
    }

    private FlowTransactionManager() {

    }
}
