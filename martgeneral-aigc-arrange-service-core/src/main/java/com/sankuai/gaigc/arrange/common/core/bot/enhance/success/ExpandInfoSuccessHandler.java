package com.sankuai.gaigc.arrange.common.core.bot.enhance.success;

import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.BotEnhanceItem;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.ExpandInfoEnhance;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.enhance.BotSuccessEnhanceHandler;
import com.sankuai.gaigc.arrange.common.core.bot.enhance.start.ExpandInfoEnhanceHandler;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import com.sankuai.gaigc.arrange.common.enums.AgentModeEnum;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.util.*;
@Slf4j
@Component
public class ExpandInfoSuccessHandler implements BotSuccessEnhanceHandler {
    @Autowired
    private ExpandInfoEnhanceHandler expandInfoEnhanceHandler;

    @Override
    public void handle(BotEnhanceItem enhanceConfig, BotRunResult runResult, BotRunParam runParam, AIBot bot) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (!(enhanceConfig instanceof ExpandInfoEnhance)) {
            stopWatch.stop();
            return;
        }

        ExpandInfoEnhance expandInfoEnhance = (ExpandInfoEnhance) enhanceConfig;
        if (!expandInfoEnhance.getEnableSerial()) {
            stopWatch.stop();
            return;
        }
        if (!expandInfoEnhanceHandler.condition(expandInfoEnhance, runParam)) {
            stopWatch.stop();
            return;
        }
        List<Map<String, Object>> contents = expandInfoEnhanceHandler.recommendOnKnowledgeBase(expandInfoEnhance, runParam, runResult);
        // 关键词高亮处理
        expandInfoEnhanceHandler.highlightKeywords(contents, expandInfoEnhance, runParam);
        runResult.getEnhanceResult().setExpandInfos(contents);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(contents) || !StreamInfoHolder.availableStream(runParam.getStream())) {
            return;
        }
        // 发送流式消息
        SSEMessage sseMessage = SSEMessageUtils.generateExpendInofsMessage(contents, stopWatch.getTotalTimeSeconds(), SSEMessageUtils.generateSectionId());
        sseMessage.setIs_last_one(AgentModeEnum.MULTIPLE_INTENTION_EXECUTE == bot.getAgent().mode());
        try {
            StreamInfoHolder.getEmitter().send(sseMessage);
        } catch (IOException e) {
            log.error("流式输出出错, botId:{}, e=", runParam.getBotId(), e);
        }
    }

    @Override
    public String relateConfigClassName() {
        return ExpandInfoEnhance.class.getSimpleName();
    }

}
