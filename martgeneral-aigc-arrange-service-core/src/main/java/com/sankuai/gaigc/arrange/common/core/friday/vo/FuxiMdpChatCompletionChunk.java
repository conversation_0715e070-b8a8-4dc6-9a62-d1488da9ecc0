package com.sankuai.gaigc.arrange.common.core.friday.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.mdp.langmodel.component.entity.MdpChatCompletionChunk;
import lombok.Data;

import java.util.List;

/**
 * @ClassName MdpChatCompletionChunkV2
 * <AUTHOR>
 * @Date 2025/2/17 下午3:28
 */
@Data
public class FuxiMdpChatCompletionChunk {
    @JsonProperty("id")
    private String id;

    @JsonProperty("object")
    private String object;

    @JsonProperty("created")
    private long created;

    @JsonProperty("model")
    private String model;

    @JsonProperty("choices")
    private List<ChoiceVO> choices;

    @JsonProperty("content")
    private String content;

    @JsonProperty("usage")
    private MdpChatCompletionChunk.UsageVO usage;

    @JsonProperty("lastOne")
    private boolean lastOne;

    @Data
    public static class ChoiceVO {
        @JsonProperty("delta")
        private DeltaVO delta;

        @JsonProperty("index")
        private int index;

        @JsonProperty("finish_reason")
        private String finishReason;
    }

    @Data
    public static class DeltaVO {
        @JsonProperty("role")
        private String role;

        @JsonProperty("content")
        private String content;

        @JsonProperty("function_call")
        private FunctionCall functionCall;

        @JsonProperty("reasoning_content")
        private String reasoningContent;
    }

    @Data
    public static class FunctionCall {
        @JsonProperty("name")
        private String name;

        @JsonProperty("arguments")
        private String arguments;
    }


}