package com.sankuai.gaigc.arrange.common.core.promptflow.entity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class BaiduLLMChatResponse {
    /**
     * 本轮对话的id
     */
    private String id;

    /**
     * 回包类型
     */
    private String object;

    /**
     * 时间戳
     */
    private Integer created;

    /**
     * 表示当前子句的序号。只有在流式接口模式下会返回该字段
     */
    @SerializedName("sentence_id")
    private Integer sentenceId;

    /**
     * 表示当前子句是否结束。只有在流式接口模式下会返回该字段
     */
    @SerializedName("is_end")
    private Boolean isEnd;

    /**
     * 当前生成的结果是否被截断
     */
    @SerializedName("is_truncated")
    private Boolean isTruncated;

    /**
     * 输出内容标识,
     */
    @SerializedName("finish_reason")
    private String finishReason;

    /**
     * 对话返回结果
     */
    private String result;

    /**
     * 表示用户输入是否存在安全，是否关闭当前会话，清理历史会话信息
     */
    @SerializedName("need_clear_history")
    private Boolean needClearHistory;

    /**
     * 当need_clear_history为true时，此字段会告知第几轮对话有敏感信息，如果是当前问题，ban_round=-1
     */
    @SerializedName("ban_round")
    private Integer banRound;

    /**
     * 0：正常返回；其他：非正常
     * */
    private Integer flag;

    /**
     * token统计信息
     */
    private Usage usage;

    /**
     * 错误码。说明：响应失败时返回该字段，成功时不返回
     */
    private String error;

    /**
     * 错误描述信息，帮助理解和解决发生的错误。说明：响应失败时返回该字段，成功时不返回
     */
    @SerializedName("error_description")
    private String errorDescription;

    @Data
    public static class Usage {
        @SerializedName("prompt_tokens")
        private Integer promptTokens;
        @SerializedName("completion_tokens")
        private Integer completionTokens;
        @SerializedName("total_tokens")
        private Integer totalTokens;
    }
}
