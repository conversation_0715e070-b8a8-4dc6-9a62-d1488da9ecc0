/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.constant.KnowledgeFieldConstants;
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.CmsStorageInfo;
import com.sankuai.gaigc.arrange.remote.build.DataStreamWriteService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

/**
 * 知识删除
 *
 * <AUTHOR>
 * @created 2024/12/6
 */
@Slf4j
@PromptFlowComponent(name = "KnowledgeDelete", desc = "知识删除", type = ComponentTypeEnum.EMBEDDING_STORE, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "knowledgeBaseId", desc = "知识库ID", type = ParamTypeEnum.STRING, required = true, category = CategoryEnum.DEFAULT),
        @Param(name = "docId", desc = "数据ID", type = ParamTypeEnum.LONG, required = false, category = CategoryEnum.DEFAULT),
        @Param(name = "originContent", desc = "原始内容", type = ParamTypeEnum.MAP, required = false, category = CategoryEnum.CUSTOM),
})

@OutputParamDefinition({
        @Param(name = "result", desc = "是否成功", type = ParamTypeEnum.BOOLEAN, required = true),
})
public class KnowledgeDelete extends AbstractComponent {
    private static final String RESULT = "result";
    @Resource
    private AIBotKnowledgeBaseService knowledgeBaseService;
    @Resource
    private DataStreamWriteService dataStreamWriteService;
    @Resource
    private KnowledgeBaseScalarQuery knowledgeBaseScalarQuery;

    @Override
    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception {
        Map<String, Object> result = Maps.newHashMap();

        String knowledgeBaseId = (String) parseParam("knowledgeBaseId", componentInfo);
        Long docId = (Long) parseParam("docId", componentInfo);
        Map<String, Object> originContent = (Map<String, Object>) Optional.ofNullable(parseParam("originContent", componentInfo)).orElse(Maps.newHashMap());
        if (Objects.nonNull(docId)) {
            originContent.put(KnowledgeFieldConstants.DOC_ID, docId);
        }
        Preconditions.checkArgument(MapUtils.isNotEmpty(originContent), "删除数据不可为空");

        List<KnowledgeBase> knowledgeBases = knowledgeBaseService.queryByIds(Lists.newArrayList(Long.parseLong(knowledgeBaseId)));
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(knowledgeBases), "知识库[" + knowledgeBaseId + "]不存在");
        knowledgeBaseScalarQuery.checkAccessAuth(componentInfo.getAppId(), knowledgeBases);
        KnowledgeBase knowledgeBase = knowledgeBases.get(0);

        Transaction tran = Cat.newTransaction("KnowledgeDelete", String.valueOf(knowledgeBase.getId()));
        try {
            boolean success = textDelete(knowledgeBase, originContent);
            result.put(RESULT, success);
            return result;
        } catch (Exception e) {
            log.error("知识库删除出错, knowledgeBaseId:{}, docId:{}, originContent:{}, e=", knowledgeBase, docId, GsonUtil.toJson(originContent), e);
            tran.setStatus(e);
            throw e;
        } finally {
            tran.complete();
        }
    }

    private boolean textDelete(KnowledgeBase knowledgeBase, Map<String, Object> originContent) {
        CmsStorageInfo cmsStorageInfo = knowledgeBase.getStorageInfo().getCmsStorageInfo();
        Preconditions.checkArgument(Objects.nonNull(cmsStorageInfo) && Objects.nonNull(cmsStorageInfo.getDataStreamId()), "cms storage info cannot be empty");
        return dataStreamWriteService.textDelete(cmsStorageInfo.getDataStreamId(), originContent);
    }
}