package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.alibaba.fastjson.JSONArray;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.swan.udqs.api.*;
import com.sankuai.swan.udqs.api.integration.IntegrationParams;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;


import java.util.*;

/**
 * @Author: wenhao10
 * @Description:
 * @Date: 2023-09-14 21:00
 */
@PromptFlowComponent(name = "DataQuery", desc = "数据查询组件", type = ComponentTypeEnum.DATA_QUERY, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "queryKey", desc = "Swan数据查询的key", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "inputs", desc = "用户自定义输入查询条件（需和swan保持一致）", type = ParamTypeEnum.MAP, category = CategoryEnum.CUSTOM, required = true)
})
@OutputParamDefinition({
        @Param(name = "code", desc = "Swan服务查询状态码", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "data", desc = "Swan数据查询结果", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "success", desc = "Swan查询是否成功", type = ParamTypeEnum.BOOLEAN, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "errorMessage", desc = "Swan查询失败错误信息", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
})
public class DataQuery extends AbstractComponent {

    private static final int DAAS_BIZ_TYPE_ID = 1103;

    private static String CLASS_MARK = DataQuery.class.getSimpleName();
    @Override
    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception{
        HashMap<String, Object> result = Maps.newHashMap();
        String queryKey = (String) parseParam("queryKey", componentInfo);
        Map<String, String> inputParamMap = (Map<String, String>) parseParam("inputs", componentInfo);
        //处理数组类型的查询input（如有）
        List<Map<String, Object>> inputList = processMultiQueryMap(inputParamMap);

        List<Map<String, Object>> allResult = Lists.newArrayList();
        for (Map<String, Object> param : inputList) {
            SwanParam swanParam = new SwanParam();
            List<Map<String, Object>> requestParams = new ArrayList<>();
            requestParams.add(param);
            swanParam.setRequestParams(requestParams);
            IntegrationParams integrationParams = new IntegrationParams();
            Map<String, Object> params = new HashMap<>();
            params.put("swanParam", swanParam);
            integrationParams.setParams(params);
            Result<QueryData> swanResult = SwanQuerier.swanQueryWithoutFormat(DAAS_BIZ_TYPE_ID, queryKey, integrationParams);
            result.put("code", swanResult.getCode());
            result.put("success", swanResult.isIfSuccess());
            if(!swanResult.isIfSuccess()) {
                result.put("data", new ArrayList<>());
                result.put("errorMessage", swanResult.getErrorMsg());
                Cat.logEvent(CLASS_MARK, "Query swan failed: {queryKey=" + queryKey + ",bizTypeId=" + DAAS_BIZ_TYPE_ID + "}errorMessage=" + swanResult.getErrorMsg());
                return result;
            }

            QueryData queryData = swanResult.getData();
            List<Map<String, Object>> resultList = queryData.getResultSet();
            if(CollectionUtils.isNotEmpty(resultList)){
                allResult.addAll(resultList);
            }
        }


        result.put("data", allResult);
        result.put("errorMessage", "");
        return result;
    }


    /**
     * 特殊处理json数组类型的inputs（swan不支持pkey的批量操作，组件把里特殊处理数组类的value。）
     * 分离非 JSON 数组和 JSON 数组元素，然后通过递归生成所有可能的组合。
     * @param inputParamMap
     * @return
     */
    public List<Map<String, Object>> processMultiQueryMap(Map<String, String> inputParamMap) {
        // 存储非JSON数组的键值对
        Map<String, String> nonArrayElements = new HashMap<>();
        // 存储JSON数组的键和对应的数组
        Map<String, JSONArray> arrayElements = new HashMap<>();

        for (Map.Entry<String, String> entry : inputParamMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            if (isJsonArray(value)) {
                // 如果值是JSON数组，将其加入到arrayElements
                arrayElements.put(key, JSONArray.parseArray(value));
            } else {
                // 否则，加入到nonArrayElements
                nonArrayElements.put(key, value);
            }
        }

        // 生成所有可能的组合
        List<Map<String, Object>> resultList = new ArrayList<>();
        generateCombinations(resultList, new HashMap<>(nonArrayElements), arrayElements, new ArrayList<>(arrayElements.keySet()), 0);

        return resultList;
    }

    private void generateCombinations(
            List<Map<String, Object>> resultList,
            Map<String, String> currentMap,
            Map<String, JSONArray> arrayElements,
            List<String> arrayKeys,
            int index) {

        if (index == arrayKeys.size()) {
            resultList.add(new HashMap<>(currentMap));
            return;
        }

        String key = arrayKeys.get(index);
        JSONArray array = arrayElements.get(key);

        for (int i = 0; i < array.size(); i++) {
            currentMap.put(key, array.getString(i));
            generateCombinations(resultList, currentMap, arrayElements, arrayKeys, index + 1);
        }
    }

    private boolean isJsonArray(String value){
        return value.startsWith("[") && value.endsWith("]");
    }
}
