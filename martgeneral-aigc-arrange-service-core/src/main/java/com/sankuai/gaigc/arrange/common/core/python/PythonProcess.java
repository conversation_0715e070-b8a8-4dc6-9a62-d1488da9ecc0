package com.sankuai.gaigc.arrange.common.core.python;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.dianping.lion.client.Lion;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.gaigc.arrange.common.constant.ServiceConstant;
import com.sankuai.meishi.stgy.algoplatform.pythonsdk.codeloader.GitCodeLoader;
import com.sankuai.meishi.stgy.algoplatform.pythonsdk.interpreter.PythonInterpreterFactory;
import com.sankuai.meishi.stgy.algoplatform.pythonsdk.monitor.PythonMonitorService;
import java.util.Map;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: wenhao10
 * @Description:
 * @Date: 2023-08-23 11:30
 */
@Slf4j
@Configuration
@ComponentScan("com.sankuai.meishi.stgy.algoplatform.pythonsdk")
public class PythonProcess {
    private GitCodeLoader gitCodeLoader;

    @PostConstruct
    public void getPythonCode() {
        try {
            String pk = Kms.getByName(ServiceConstant.APP_KEY, "git_ssh_id_rsa");
            String pbk = Kms.getByName(ServiceConstant.APP_KEY, "git_ssh_id_rsa_pub");
            gitCodeLoader = new GitCodeLoader(ServiceConstant.GIT_URL, pk, pbk, ServiceConstant.GIT_USER_NAME);
            boolean pullCodeStatu = gitCodeLoader.pullCode(Lion.getConfigRepository().get("commitId"));
            if (!pullCodeStatu) {
                log.info("code pull failed");
            }
        } catch (KmsResultNullException e) {
            throw new RuntimeException(e);
        }


    }

    @Bean
    public String pythonRuntimeStatus() {
        // 建议配置在lion上，服务启动时加载
        String pythonConfigJson = Lion.getConfigRepository().get("pythonConfigJson");
        PythonInterpreterFactory.start(pythonConfigJson, false);
        return "ok";
    }

    @Bean
    public String pythonMonitorStatus(String pythonRuntimeStatus) {
        PythonMonitorService.startReportHeartbeat();
        PythonMonitorService.startCheckAndRecover();
        return "ok";
    }

    public String executePython(String compPythonPath, String method, Map<String, Object> pythonParams) {
        String commitId = Lion.getConfigRepository().get("commitId");
        gitCodeLoader.pullCode(commitId);
        String path = "pythoncode." + commitId + "." + compPythonPath;
        String argJson = JSON.toJSONString(pythonParams);
        String pythonConfigJson = Lion.getConfigRepository().get("pythonConfigJson");
        JSONArray pythonConfigs = JSON.parseArray(pythonConfigJson);
        String interpreterName = pythonConfigs.getJSONObject(0).getString("name");
        return PythonInterpreterFactory.getRuntime(interpreterName).getBridgeInstance().invoke(path, method, argJson);
    }
}