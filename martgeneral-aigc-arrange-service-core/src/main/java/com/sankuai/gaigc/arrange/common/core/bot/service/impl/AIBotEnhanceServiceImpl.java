/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.service.impl;

import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotEnhanceService;
import com.sankuai.gaigc.arrange.dao.dal.dto.enhance.AIBotEnhanceDto;
import com.sankuai.gaigc.arrange.dao.dal.entity.AIBotEnhanceDO;
import com.sankuai.gaigc.arrange.dao.dal.example.AIBotEnhanceDOExample;
import com.sankuai.gaigc.arrange.dao.dal.mapper.AIBotEnhanceDOMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 聊天增强服务
 *
 * <AUTHOR>
 * @created 2024/8/16
 */
@Service
@Component
public class AIBotEnhanceServiceImpl implements AIBotEnhanceService {
    @Resource
    private AIBotEnhanceDOMapper aiBotEnhanceDOMapper;

    @Override
    public List<AIBotEnhanceDto> getAll() {
        AIBotEnhanceDOExample example = new AIBotEnhanceDOExample();
        example.createCriteria().andIdGreaterThan(0L);
        List<AIBotEnhanceDO> list = aiBotEnhanceDOMapper.selectByExampleWithBLOBs(example);
        return list.stream().map(enhanceDO -> {
            AIBotEnhanceDto enhanceDto = new AIBotEnhanceDto();
            enhanceDto.setId(enhanceDO.getId());
            enhanceDto.setName(enhanceDO.getName());
            enhanceDto.setClassName(enhanceDO.getClassName());
            return enhanceDto;
        }).collect(Collectors.toList());
    }
}