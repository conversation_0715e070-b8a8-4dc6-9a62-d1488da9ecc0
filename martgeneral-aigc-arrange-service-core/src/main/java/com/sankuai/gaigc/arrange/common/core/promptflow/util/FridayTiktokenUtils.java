package com.sankuai.gaigc.arrange.common.core.promptflow.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ComponentConstants;
import com.sankuai.gaigc.arrange.common.util.HttpUtils;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.reflections.Reflections.log;

@Slf4j
public class FridayTiktokenUtils {
    private static final String BASE_URL = "https://aigc.sankuai.com";
    private static final String OPEN_MODEL_API = "/v1/openai/tiktoken";
    private static final String CLASS_MARK = FridayTiktokenUtils.class.getSimpleName();

    public static Map<String, Object> queryTiktoken(Map<String, Object> param) throws Exception {
        String url = BASE_URL + OPEN_MODEL_API;
        Map<String, String> header = Maps.newHashMap();
        header.put("Authorization", ComponentConstants.DEFAULT_APP_ID);
        String fridayResponse = HttpUtils.getPostResponse(url, JSON.toJSONString(param), header);
        Map<String, Object> resultMap = StringUtils.isNotEmpty(fridayResponse) ?
                JSON.parseObject(fridayResponse, new TypeReference<Map<String, Object>>() {}) : Maps.newHashMap();
        return resultMap;
    }

    public static Integer queryComponentToken(String componentName, Map<String, Object> inputMap) {
        if(StringUtils.equals(componentName, "GeneralLLM") || StringUtils.equals(componentName, "AdvanceLLM")) {

            String userPrompt = String.valueOf(inputMap.get("userPrompt"));
            String systemPrompt = String.valueOf(inputMap.get("systemPrompt"));
            String modelName = String.valueOf(inputMap.get("modelName"));

            try {
                Map<String, Object> tiktokenRequest = Maps.newHashMap();
                StringBuilder sb = new StringBuilder();
                tiktokenRequest.put("prompt", sb.append(userPrompt).append(systemPrompt).toString());
                tiktokenRequest.put("model", modelName);
                Map<String, Object> tiktokenResponse = queryTiktoken(tiktokenRequest);
                if(Objects.equals(tiktokenResponse.get("code"), 0) && tiktokenResponse.get("data") != null) {
                    JSONObject data = (JSONObject) tiktokenResponse.get("data");
                    return data.getInteger("token_len");
                }
            } catch (Exception e) {
                Cat.logError(CLASS_MARK, e);
                log.error(e.getMessage());
            }
        }
       return 0;
    }
}
