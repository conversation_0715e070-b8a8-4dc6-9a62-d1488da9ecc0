package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod;

/**
 * <AUTHOR>
 * @date 2024/11/14 10:19
 */

import com.sankuai.gaigc.arrange.api.entity.Response;
import com.sankuai.gaigc.arrange.api.service.PromptFlowExecutionService;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.promptflow.function.FunctionCallNotExecuteResult;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.config.MccConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 门票购票小助手相关插件
 * 封装Flow
 * */
@Service
@Slf4j
public class TicketOriginalInfoService {
    private static final String SCENIC_INFO_INQUIRY = "scenic_info_inquiry";

    private static final String TICKET_INFO_INQUIRY = "ticket_info_inquiry";

    private static final String TICKET_RECOMMEND = "ticket_recommend";

    @Resource(name = "promptFlowExecutionServiceImpl")
    private PromptFlowExecutionService promptFlowExecutionService;

    @Autowired
    private MccConfig mccConfig;
    /**
     * 景区信息咨询工具-scenic_info_inquiry
     * @param rewrite_query 将用户的不规范口语或方言，改写为便于理解的书面用语
     * @param poiId poiId
     * @param userInput 用户本轮输入信息
     * @param historyMessages 历史对话信息，格式[{"role":"user","message":"你好"},{"role":"assistant","message":"你好！有什么我可以帮你的吗？关于景点和购票问题，可以来问我哦～"},{"role":"user","message":"打铁花是几点"},{"role":"assistant","message":"抱歉，我无法回答您的问题。建议您直接致电景区咨询相关信息。"},{"role":"user","message":"什么时候开门"},{"role":"assistant","message":"婺女洲度假区全年无休，每天的开园时间是00:00，闭园时间是23:59。"},{"role":"user","message":"打铁花是几点"}]
     * */
    public Map<String, Object> scenicInfoInquiry(String rewrite_query, String poiId, String userInput, List<Map<String, Object>> historyMessages) {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("rewrite_query", rewrite_query);
        Map<String, Object> flowInputMap = new HashMap<>();
        flowInputMap.put("poiId", poiId);
        flowInputMap.put("userInput", userInput);
        flowInputMap.put("userMessages", historyMessages);
        flowInputMap.put("userIntent", createUserIntent(arguments,SCENIC_INFO_INQUIRY));

        Long flowId = mccConfig.getTicketInfoPluginFlowIds().get(SCENIC_INFO_INQUIRY);
        SseEmitter emitter = StreamInfoHolder.getEmitter();
        Response<Map<String, Object>> response = (emitter == null) ?
                promptFlowExecutionService.runFlow(flowId, flowInputMap) :
                promptFlowExecutionService.runFlowStream(flowId, flowInputMap, new HashMap<>(), emitter);

        return response.getData();
    }

    private Map<String, Object> createUserIntent(Map<String, Object> arguments,String intentName) {
        FunctionCallNotExecuteResult intentResult = new FunctionCallNotExecuteResult();
        intentResult.setName(intentName);
        intentResult.setArguments(arguments);
        Map<String, Object> userIntent = new HashMap<>();
        userIntent.put("answer", GsonUtil.toJson(intentResult));
        return userIntent;
    }

    /**
     * 门票相关信息咨询-ticket_info_inquiry
     * @param ticketPrice 请筛选出问题中涉及到的门票价格
     * @param ticketType 筛选出问题中涉及到的门票类型（例如成人票、老人票、儿童票、学生票、交通票、讲解票、退役军人票等）
     * @param ticketPreference 筛选出问题中涉及到的用户偏好（交通、入口）
     * @param rewriteQuery 将用户的不规范口语或方言，改写为便于理解的书面用语
     * @param poiId poiId
     * @param userInput 用户本轮输入信息
     * @param historyMessages 历史对话信息，格式[{"role":"user","message":"你好"},{"role":"assistant","message":"你好！有什么我可以帮你的吗？关于景点和购票问题，可以来问我哦～"},{"role":"user","message":"打铁花是几点"},{"role":"assistant","message":"抱歉，我无法回答您的问题。建议您直接致电景区咨询相关信息。"},{"role":"user","message":"什么时候开门"},{"role":"assistant","message":"婺女洲度假区全年无休，每天的开园时间是00:00，闭园时间是23:59。"},{"role":"user","message":"打铁花是几点"}]
     * */
    public Map<String, Object> ticketInfoInquiry(String ticket_price, String ticket_type, String ticket_preference,
                                                 String rewrite_query, String poiId, String userInput,
                                                 List<Map<String, Object>> historyMessages) {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("rewrite_query", rewrite_query);
        arguments.put("ticket_price", ticket_price);
        arguments.put("ticket_type", ticket_type);
        arguments.put("ticket_preference", ticket_preference);
        Map<String, Object> flowInputMap = new HashMap<>();
        flowInputMap.put("poiId", poiId);
        flowInputMap.put("userInput", userInput);
        flowInputMap.put("userMessages", historyMessages);
        flowInputMap.put("userIntent", createUserIntent(arguments,TICKET_INFO_INQUIRY));

        Long flowId = mccConfig.getTicketInfoPluginFlowIds().get(TICKET_INFO_INQUIRY);
        SseEmitter emitter = StreamInfoHolder.getEmitter();
        Response<Map<String, Object>> response = (emitter == null) ?
                promptFlowExecutionService.runFlow(flowId, flowInputMap) :
                promptFlowExecutionService.runFlowStream(flowId, flowInputMap, new HashMap<>(), emitter);

        return response.getData();
    }

    /**
     * 门票相关信息咨询-ticket_recommend
     * @param foreignerPeople 外国人或港澳华侨的数量，通常指外籍游客或港澳人士。
     * @param stuPeople 学生的数量，指小学生、初中生、高考生、大学生、研究生和成年的高中生。准大学生不属于大学生。
     * @param stuAge 学生的年龄，若有则抽取出来，多个学生返回array。
     * @param stuType 学生的类型，如小学生、初中生、高中生、大学生、研究生、博士生等，若有则抽取出来，多个学生类型返回array。
     * @param childrenPeople 儿童/未成年人的数量（小于18岁才算儿童/未成年人）。
     * @param childrenAge 儿童/未成年人的年龄，若有则抽取出来，不需要加单位，多个儿童/未成年人返回array。
     * @param childrenHeight 儿童/未成年人的身高(例如1.6米，150)，若有则抽取出来，单位是厘米，不需要加单位，多个儿童/未成年人返回array。身高超过xxcm识别为x+1cm。
     * @param oldPeople 老人的数量。
     * @param oldAge 老人的年龄，若有则抽取出来，多个老人返回array。
     * @param cPeople 成年人的数量（18岁以上，包含毕业大学生，未满19算18岁成年人）。儿童、老人、外国人、高考生、港澳人士不可重复归类为成年人c_people。没有则空。
     * @param ticketPrice 请筛选出问题中涉及到的门票价格，单位是元，不需要加单位，多个返回array。
     * @param ticketType 请筛选出问题中涉及到的门票类型（例如成人票、老人票、儿童票、学生票、交通票、讲解票等），多个返回array。
     * @param ticketPreference 请筛选出问题中涉及到的用户偏好（交通、入口等），多个返回array。
     * @param rewriteQuery 将用户的不规范口语或方言，改写为便于理解的书面用语
     * @param poiId poiId
     * @param userInput 用户本轮输入信息
     * @param historyMessages 历史对话信息，格式[{"role":"user","message":"你好"},{"role":"assistant","message":"你好！有什么我可以帮你的吗？关于景点和购票问题，可以来问我哦～"},{"role":"user","message":"打铁花是几点"},{"role":"assistant","message":"抱歉，我无法回答您的问题。建议您直接致电景区咨询相关信息。"},{"role":"user","message":"什么时候开门"},{"role":"assistant","message":"婺女洲度假区全年无休，每天的开园时间是00:00，闭园时间是23:59。"},{"role":"user","message":"打铁花是几点"}]
     * */
    public Map<String, Object> ticketRecommend(Integer foreigner_people, Integer stu_people, String stu_age, String stu_type, Integer children_people, String children_age,
                                               String children_height, Integer old_people, String old_age, Integer c_people, String ticket_price, String ticket_type,
                                               String ticket_preference, String rewrite_query, String poiId, String userInput, List<Map<String, Object>> historyMessages) {

        Map<String, Object> arguments = new HashMap<>();
        if (Objects.nonNull(foreigner_people)) {
            arguments.put("foreigner_people", foreigner_people);
        }
        if (Objects.nonNull(stu_people)) {
            arguments.put("stu_people", stu_people);
        }
        if (Objects.nonNull(stu_age)) {
            arguments.put("stu_age", stu_age);
        }
        if (Objects.nonNull(stu_type)) {
            arguments.put("stu_type", stu_type);
        }
        if (Objects.nonNull(children_people)) {
            arguments.put("children_people", children_people);
        }
        if (Objects.nonNull(children_age)) {
            arguments.put("children_age", children_age);
        }
        if (Objects.nonNull(children_height)) {
            arguments.put("children_height", children_height);
        }
        if (Objects.nonNull(old_people)) {
            arguments.put("old_people", old_people);
        }
        if (Objects.nonNull(old_age)) {
            arguments.put("old_age", old_age);
        }
        if (Objects.nonNull(c_people)) {
            arguments.put("c_people", c_people);
        }
        if (Objects.nonNull(ticket_price)) {
            arguments.put("ticket_price", ticket_price);
        }
        if (Objects.nonNull(ticket_type)) {
            arguments.put("ticket_type", ticket_type);
        }
        if (Objects.nonNull(ticket_preference)) {
            arguments.put("ticket_preference", ticket_preference);
        }
        arguments.put("rewrite_query", rewrite_query);
        Map<String, Object> flowInputMap = new HashMap<>();
        flowInputMap.put("poiId", poiId);
        flowInputMap.put("userInput", userInput);
        flowInputMap.put("userMessages", historyMessages);
        flowInputMap.put("userIntent", createUserIntent(arguments,TICKET_RECOMMEND));

        Long flowId = mccConfig.getTicketInfoPluginFlowIds().get(TICKET_RECOMMEND);
        SseEmitter emitter = StreamInfoHolder.getEmitter();
        Response<Map<String, Object>> response = (emitter == null) ?
                promptFlowExecutionService.runFlow(flowId, flowInputMap) :
                promptFlowExecutionService.runFlowStream(flowId, flowInputMap, new HashMap<>(), emitter);

        return response.getData();
    }

}