/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension;

import com.meituan.nibhtp.os.htp.cms.access.common.search.Expression;
import java.util.List;
import lombok.Data;

/**
 * Bot知识库配置
 *
 * <AUTHOR>
 * @created 2024/10/13
 */
@Data
public class BotKnowledgeBaseItem {
    /** 知识库ID */
    private Long knowledgeBaseId;
    /** 最小匹配度 */
    private Double similarityThreshold;
    /** 最大召回数量 */
    private Integer topK;
    /** 标量过滤表达式 */
    private Expression scalarFilterExp;
    /** 扩展召回配置 */
    private BotKnowledgeBaseRecallExtConfig recallExtConfig;
    /** 鉴权配置 */
    private BotKnowledgeAuthConfig authConfig;
    /** 字段加工 */
    private List<FieldProcessingItemConfig> processConfig;
}