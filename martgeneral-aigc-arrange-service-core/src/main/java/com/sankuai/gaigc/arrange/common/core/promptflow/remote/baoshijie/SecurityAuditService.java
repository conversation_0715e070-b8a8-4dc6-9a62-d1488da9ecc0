package com.sankuai.gaigc.arrange.common.core.promptflow.remote.baoshijie;

import com.alibaba.fastjson.JSON;
import com.dianping.credit.access.api.RiskInfoService;
import com.sankuai.gaigc.arrange.common.core.monitor.AppOpsService;
import com.sankuai.gaigc.arrange.common.core.monitor.BaoShiJieSecCheckContentRangeRaptor;
import com.sankuai.gaigc.arrange.common.core.promptflow.enums.baoshijie.*;
import com.sankuai.gaigc.arrange.common.enums.AppEntityTypeEnum;
import com.sankuai.gaigc.arrange.common.enums.InputOutPutTypeEnum;
import com.sankuai.gaigc.arrange.common.util.DateUtils;
import com.sankuai.gaigc.arrange.config.MccConfig;
import com.sankuai.gaigc.arrange.dao.dal.dto.app.BaoshijieWhiteListConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保时洁工具类
 * 文档: https://km.sankuai.com/page/235494362
 * 接口文档: https://km.sankuai.com/page/235494362
 * 张俊杰 2024年09月24日16:04:07
 */
@Slf4j
@Component
@SuppressWarnings("JavadocReference") // 忽略文档注释警告
public class SecurityAuditService {
    /**
     * 保时洁服务
     */
    @Resource
    private RiskInfoService riskInfoService;

    @Resource
    private MccConfig mccConfig;

    private static final String CONTENT_SEPARATOR = ";";
    @Resource
    private AppOpsService appOpsService;

    /**
     * 检查文本内容是否符合保时洁的要求
     *
     * @param appId
     * @param entityId
     * @param appEntityTypeEnum
     * @param baoShiJieDataSourceEnum
     * @param baoShiJieInputOutPutTypeEnum
     * @param contents                     可以为null ,为null就不校验,
     */
    public BaoShiJieAuditResult riskCheck(Long appId, Long entityId, AppEntityTypeEnum appEntityTypeEnum, BaoShiJieDataSourceEnum baoShiJieDataSourceEnum,
                                          InputOutPutTypeEnum baoShiJieInputOutPutTypeEnum, List<String> contents) {
        Map<String, Object> auditRequest = null;
        Map<String, Object> auditResult = null;
        try {
            // 如果内容数组为空，则直接返回，不进行校验
            if (CollectionUtils.isEmpty(contents)) {
                log.warn("保时洁校验内容数组为空,直接跳过保时洁校验");
                return new BaoShiJieAuditResult();

            }
            //保时洁功能系统关闭
            if (!isEnableBaoShiJieInputAudit()) {
                return new BaoShiJieAuditResult();
            }
            //过滤掉数字 并 使用;拼接contents
            String contentStr = processContent(contents);
            //白名单校验
            if (isWhiteListId(appId, entityId, appEntityTypeEnum, baoShiJieInputOutPutTypeEnum)) {
                BaoShiJieSecCheckContentRangeRaptor.recordRange("skip_white_list", appEntityTypeEnum, entityId, contentStr.length());
                return new BaoShiJieAuditResult();
            }
            // 如果内容长度大于2000，则直接返回，不进行校验
            if (contentStr.length() > 2000){
                BaoShiJieSecCheckContentRangeRaptor.recordRange("skip_gt_2000", appEntityTypeEnum, entityId, contentStr.length());
                log.warn("保时洁送审跳过，内容长度大于2000,entity:{} contentStr:{}", appEntityTypeEnum.getDesc() + "Id:" + entityId, contentStr);
                return new BaoShiJieAuditResult();
            }

            //构建请求参数
            auditRequest = buildRequestMap(baoShiJieDataSourceEnum, contentStr);
            log.info("调用保时洁接口入参: entity:{}, param:{}", appEntityTypeEnum.getDesc() + "Id:" + entityId, JSON.toJSONString(auditRequest));
            // 调用保时洁接口
            auditResult = sendBaoshijie(auditRequest);
            //打点记录待送审长度
            BaoShiJieSecCheckContentRangeRaptor.recordRange("have_send", appEntityTypeEnum, entityId, contentStr.length());
            //结果解析
            BaoShiJieAuditResult baoShiJieAuditResult = parseResult(auditResult);
            if (baoShiJieAuditResult.isHaveRiskWords()){
                log.error("保时洁审核有风险: entity:{}, 输入输出类型:{}, param:{},result:{}",
                        appEntityTypeEnum.getDesc() + "Id:" + entityId,
                        baoShiJieInputOutPutTypeEnum.getValue(),
                        JSON.toJSONString(auditRequest),
                        JSON.toJSONString(auditResult));
            }
            return baoShiJieAuditResult;
        }catch (Exception e){
            log.error("保时洁校验异常,entity:{},输入输出类型:{},param:{},result:{} ",
                    appEntityTypeEnum.getDesc() + "Id:" + entityId,
                    baoShiJieInputOutPutTypeEnum.getValue(),
                    auditRequest != null ? JSON.toJSONString(auditRequest) : "",
                    auditResult != null ? JSON.toJSONString(auditResult):"", e);
            return new BaoShiJieAuditResult();
        }
    }

    private String processContent(List<String> contents) {
        return contents.stream().filter(content -> !StringUtils.isNumeric(content))
                .map(x -> StringUtils.replaceEach(x, new String[]{"\\", "\\/", "{", "}", "[", "]", "\""}, new String[]{"", "", "", "", "", "", ""})).collect(Collectors.joining(CONTENT_SEPARATOR));
    }

    /**
     * 判断是否在白名单中
     *
     * @param appId
     * @param entityId
     * @param appEntityTypeEnum
     * @param inputOutPutType
     * @return true 在白名单 ,  false 不在白名单
     */
    private boolean isWhiteListId(Long appId, Long entityId, AppEntityTypeEnum appEntityTypeEnum, InputOutPutTypeEnum inputOutPutType) {
        //获取白名单配置
        BaoshijieWhiteListConfigDto baoshijieWhiteListConfig = appOpsService.getBaoshijieWhiteListConfig(appId);
        //应用空间是否开始保时洁配置
        if (!baoshijieWhiteListConfig.getIsEnableBaoShiJie()) {
            return true;
        }

        //获取白名单列表
        List<BaoshijieWhiteListConfigDto.Config> configs = baoshijieWhiteListConfig.getConfigByEntityType(appEntityTypeEnum);
        Optional<BaoshijieWhiteListConfigDto.Config> first = configs.stream().filter(config -> config.getId().equals(entityId)).findFirst();
        if (!first.isPresent()) {
            return false;
        }

        //当前应用白名单作用范围
        BaoshijieWhiteListConfigDto.Config config = first.get();
        List<Integer> scope = config.getScope();
        if (CollectionUtils.isNotEmpty(scope) && scope.contains(inputOutPutType.getValue()) ) {
            return true;
        }

        return false;
    }


    private Boolean isEnableBaoShiJieInputAudit() {
        return mccConfig.getIsEnableBaoShiJieInputAudit();
    }

    /**
     * 开始进行保时洁文本校验
     *
     * @param baoShiJieDataSourceEnum      数据源枚举
     * @param baoShiJieInputOutPutTypeEnum
     * @param bodyMapList                  请求体内容列表
     */
    private Map<String, Object> sendBaoshijie(Map<String, Object> requestMap) {
        long currentTimeMillis = System.currentTimeMillis();
        // 调用风险信息服务进行校验
        Object riskInfoResult = riskInfoService.riskInfo(requestMap);
        log.info("riskInfoService.riskInfo耗时: {}  ms", System.currentTimeMillis() - currentTimeMillis);
        // 校验返回结果是否为null
        if (null == riskInfoResult) {
            log.error("riskInfoService.riskInfo 返回结果为null");
            throw new RuntimeException("保时洁文本校验失败");
        }
        log.info("调用保时洁接口返回结果: {}", riskInfoResult);

        // 转换返回结果为Map
        return convertRiskInfoResultMap(riskInfoResult);
    }


    /**
     * 构建请求参数Map
     *
     * @param baoShiJieDataSourceEnum 数据源枚举
     * @param body                    请求体内容列表
     * @return 构建好的请求参数Map
     */
    private Map<String, Object> buildRequestMap(BaoShiJieDataSourceEnum baoShiJieDataSourceEnum, String body) {
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("body", buildSingleBodyList(body));
        requestMap.put("ACCESS_POINT", BaoShiJieAccessPointEnum.text.getIntCode());
        Long bizId = System.currentTimeMillis() + new Random().nextInt(10000);
        //bizId请尽可能使用业务主键，请务必保证bizId的唯一性，与每条送审内容一一对应，如：图片是图片ID，评价是评价ID，POI是商户ID等。业务方与内容安全组排查case时使用。
        requestMap.put("bizId", bizId);
        //transid每条送审内容事件的唯一标识，全局唯一，业务方负责维护。
        requestMap.put("transid", bizId + "");
        requestMap.put("datasource", baoShiJieDataSourceEnum);
        //内容结果产生时间 格式要求字符串“2016-03-12 12:10:01” 这种格式的
        requestMap.put("datetime", DateUtils.format(DateUtils.timestampToDatetime(System.currentTimeMillis()), DateUtils.STANDARD_FORMAT));
        return requestMap;
    }

    private boolean isSuccessCode(Map<String, Object> riskInfoResultMap) {
        Integer code = MapUtils.getInteger(riskInfoResultMap, "code");
        return ObjectUtils.equals(code, 200);
    }

    private Map<String, Object> convertRiskInfoResultMap(Object riskInfoResult) {
        if (!(riskInfoResult instanceof Map)) {
            log.error("riskInfoResult不是Map类型,请检查");
            throw new RuntimeException("保时洁校验失败");
        }
        return (Map<String, Object>) riskInfoResult;
    }


    private List<Map<String, String>> buildSingleBodyList(String content) {
        List<Map<String, String>> body = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("value", content);
        map.put("name", "messageIndex0");
        map.put("type", BaoShiJieBodyTypeEnum.text.getType());
        body.add(map);
        return body;
    }


    private Map<String, String> buildBodyMap(String content, String name) {
        Map<String, String> map = new HashMap<>();
        map.put("value", content);
        map.put("name", name);
        map.put("type", BaoShiJieBodyTypeEnum.text.getType());
        return map;
    }

    private String generateChatMessageName(int i) {
        return "messageIndex" + i;
    }


    private BaoShiJieAuditResult parseResult(Map<String, Object> auditResult){
        // 校验返回结果是否成功
        if (!isSuccessCode(auditResult)) {
            throw new RuntimeException("保时洁文本审核调用失败");
        }

        // 获取返回结果中的pair部分
        Map<String, Object> pair = (Map<String, Object>) MapUtils.getMap(auditResult, "pair");
        Integer verifyStatus = MapUtils.getInteger(pair, "verifyStatus");
        // 判断是否命中违规标签
        if (BaoShiJieVerifyStatusEnum.isHitGotOutOfLineLabel(verifyStatus)) {
            StringJoiner violationDescStringJoiner = new StringJoiner(",");
            List<Map<String, Object>> rs = (List<Map<String, Object>>) pair.get("statusName");
            // 收集违规描述信息
            rs.stream().map(BaoShiJieViolationInfoDTO::from)
                    .filter(Objects::nonNull)
                    //过滤出命中的违规信息 (去掉疑似命中,只要精确命中的.)
                    .filter(x -> Objects.equals(x.getBaoShiJieStatusNameListStatusCodeEnum(), BaoShiJieStatusNameListStatusCodeEnum.hit))
                    .map(BaoShiJieViolationInfoDTO::getViolationDesc)
                    .forEach(violationDescStringJoiner::add);
            if (violationDescStringJoiner.length() == 0) {
                return new BaoShiJieAuditResult();
            }
            BaoShiJieAuditResult baoShiJieAuditResult = new BaoShiJieAuditResult();
            baoShiJieAuditResult.setErrorMessage(violationDescStringJoiner.toString());
            baoShiJieAuditResult.setHaveRiskWords(true);
            return baoShiJieAuditResult;
        }
        return new BaoShiJieAuditResult();
    }
}
