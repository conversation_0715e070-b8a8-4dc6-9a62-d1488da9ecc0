/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.plan;

import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.FuncExecutionResultMessage;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.core.bot.Function;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutableExecuteSkillParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutableLLMParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutableMatchSkillParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.SpecifySkill;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableExecuteSkill;
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableKnowledgeRecall;
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableMatchSkill;
import com.sankuai.gaigc.arrange.common.core.bot.stream.FunctionDirectStreamHandler;
import com.sankuai.gaigc.arrange.common.core.bot.utils.BotPromptUtil;
import com.sankuai.gaigc.arrange.common.enums.AgentModeEnum;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.dao.dal.dto.function.FunctionCallParamDto;
import com.sankuai.gaigc.arrange.dao.dal.enums.FunctionTypeEnum;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * T&RAG：工具与检索增强生成
 *
 * <AUTHOR>
 * @created 2024/5/29
 */
@Slf4j
@Component
public class ToolAndRagAgentService extends AbstractAgent {
    private static final String DEFAULT_PROMPT = "请根据参考知识回答问题\n{{bizMsgHistory}}\n{{knowledge}}\n{{knowledgeByQuestion}}";
    private static final String RAG_PLAN = "RAGCall";
    private static final String TAG_PLAN = "TAGCall";
    private static final String TAG_OR_RAG_PLAN = "TAG_RAGCall";

    @Override
    public AgentModeEnum mode() {
        return AgentModeEnum.TOOL_AND_RAG;
    }

    @Override
    public ExecutePlan makePlan(BotRunParam runParam, AIBot bot) {
        List<Function> functions = Optional.ofNullable(bot.getPlugins()).orElse(Collections.emptyList()).stream().flatMap(item -> item.getFunctionList().stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bot.getPlugins()) && CollectionUtils.isNotEmpty(bot.getKnowledgeBases())) {
            // 无插件，有知识库
            return generateRagPlan(bot);
        } else if (CollectionUtils.isEmpty(bot.getKnowledgeBases())) {
            if (CollectionUtils.isEmpty(functions)) {
                throw new IllegalArgumentException("Bot未关联知识库、技能，或工作流、插件未上线");
            } else if (functions.size() == 1 && StringUtils.equals(ExecutableMatchSkill.SPECIAL_FUNCTION_NAME, functions.get(0).getName())) {
                // 无知识库，有且仅有一个“知识库中台召回”插件
                return generateRagPlan(bot);
            }
            Optional<Function> any = functions.stream().filter(item -> StringUtils.equals(ExecutableMatchSkill.SPECIAL_FUNCTION_NAME, item.getName())).findAny();
            if (!any.isPresent()) {
                // 无知识库，有插件，没有“知识库中台召回”插件
                return generateTagPlan(runParam, bot);
            }
        }

        return generateTagOrRagPlan(runParam, bot);
    }

    @Override
    public BotRunResult parseResult(ExecutePlan plan, BotRunParam runParam, AIBot bot) {
        if (StringUtils.equals(plan.getName(), TAG_OR_RAG_PLAN)) {
            ExecuteStep skillExecuteTmpSubStep = plan.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_DIRECT.getType())).findFirst().get();
            if (!skillExecuteAbnormal(skillExecuteTmpSubStep)) {
                // 技能执行成功，清空知识库召回的错误码
                ExecuteStep knowledgeRecallTmpSubStep = plan.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.KNOWLEDGE.getType())).findFirst().get();
                if (Objects.nonNull(knowledgeRecallTmpSubStep.getExecuteResult()) && CollectionUtils.isNotEmpty(knowledgeRecallTmpSubStep.getExecuteResult().getProcessCodes())) {
                    knowledgeRecallTmpSubStep.getExecuteResult().setProcessCodes(Collections.emptyList());
                }
            }
        }

        BotRunResult botRunResult = parseLastStepIsLlmResult(plan, bot);

        if (CollectionUtils.isNotEmpty(botRunResult.getCards()) || StringUtils.isNotBlank(botRunResult.getReplyContent())) {
            if (CollectionUtils.isNotEmpty(botRunResult.getCards())) {
                botRunResult.setReplyContent(GsonUtil.toJson(botRunResult.getCards().get(0)));
                botRunResult.setProcessCode(Collections.emptyList());
            }
        }
        return botRunResult;
    }

    private ExecutePlan generateTagOrRagPlan(BotRunParam runParam, AIBot bot) {
        ExecutePlan tagOrRagPlan = new ExecutePlan();
        tagOrRagPlan.setName(TAG_OR_RAG_PLAN);

        // 1.知识库召回
        ExecuteStep knowledgeRecallStep = new ExecuteStep();
        knowledgeRecallStep.setType(BotExecutorTypeEnum.KNOWLEDGE.getType());
        ExecutionStep knowledgeRecall = new ExecutionStep();
        knowledgeRecall.setSteps(Lists.newArrayList(knowledgeRecallStep));
        // 2.技能匹配
        ExecuteStep skillMatchStep = generateSkillMatchStep(runParam, bot);
        skillMatchStep.setSkipFunction(planContext -> {
            ExecuteStep knowledgeRecallTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.KNOWLEDGE.getType())).findFirst().get();
            // 知识召回正常则跳过
            return knowledgeBaseRecallNormal(knowledgeRecallTmpSubStep);
        });
        ExecutionStep skillMatch = new ExecutionStep();
        skillMatch.setSteps(Lists.newArrayList(skillMatchStep));
        // 3.技能执行
        ExecuteStep skillExecuteStep = generateSkillExecuteStep();
        skillExecuteStep.setSkipFunction(planContext -> {
            ExecuteStep skillMatchTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_MATCH.getType())).findFirst().get();
            ExecuteResult executeResult = skillMatchTmpSubStep.getExecuteResult();
            return skillMatchTmpSubStep.isSkip() || BooleanUtils.isNotTrue(executeResult.isRecall());
        });
        ExecutionStep executeSkill = new ExecutionStep();
        executeSkill.setSteps(Lists.newArrayList(skillExecuteStep));
        // 4.大模型输出
        ExecuteStep llmStep = new ExecuteStep();
        llmStep.setType(BotExecutorTypeEnum.ANSWER.getType());
        ExecutableLLMParam llmParam = new ExecutableLLMParam();
        llmParam.setFillPromptFunction(((step, planContext) -> {
            ExecuteStep skillExecuteTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_DIRECT.getType())).findFirst().get();
            if (skillExecuteAbnormal(skillExecuteTmpSubStep)) {
                // RAG prompt
                String characterPrompt = Optional.ofNullable(BotPromptUtil.promptPoundAdjust(planContext.getBot().getCharacterPrompt())).orElse(DEFAULT_PROMPT);
                characterPrompt = BotPromptUtil.fillPrompt(characterPrompt, planContext.getRunParam());
                // 知识库结果
                ExecuteStep knowledgeRecallTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.KNOWLEDGE.getType())).findFirst().get();
                BotKnowledgeRecallResult recallResult = (BotKnowledgeRecallResult) knowledgeRecallTmpSubStep.getExecuteResult().getResult().get(ExecutableKnowledgeRecall.DATA_FIELD);
                return BotPromptUtil.fillPrompt(characterPrompt, recallResult.getPlaceHolderAndContent(), true);
            }
            Object callResult = skillExecuteTmpSubStep.getExecuteResult().getResult().get(ExecutableExecuteSkill.DATA_FIELD);
            FuncExecutionResultMessage message = new FuncExecutionResultMessage(skillExecuteTmpSubStep.getExecuteTool(), GsonUtil.toJson(callResult));
            return GsonUtil.toJson(message);
        }));
        llmParam.setConvertFunctionResult(planContext -> {
            ExecuteStep skillExecuteTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_DIRECT.getType())).findFirst().get();
            return !skillExecuteTmpSubStep.isSkip();
        });
        Optional.ofNullable(bot.getModelConfig()).ifPresent(botModelConfig -> {
            Optional.ofNullable(botModelConfig.getBotPlanModel()).ifPresent(llmParam::setFridayModelConfig);
            Optional.ofNullable(getAppId(botModelConfig.getAppId())).ifPresent(llmParam::setAppId);
        });
        llmStep.setCustomParam(llmParam);
        ExecutionStep llmReply = new ExecutionStep();
        llmReply.setSteps(Lists.newArrayList(llmStep));

        tagOrRagPlan.setExecutionSteps(Lists.newArrayList(knowledgeRecall, skillMatch, executeSkill, llmReply));
        return tagOrRagPlan;
    }

    private ExecutePlan generateTagPlan(BotRunParam runParam, AIBot bot) {
        ExecutePlan tagPlan = new ExecutePlan();
        tagPlan.setName(TAG_PLAN);

        // 1.技能匹配
        ExecutionStep skillMatch = new ExecutionStep();
        skillMatch.setSteps(Lists.newArrayList(generateSkillMatchStep(runParam, bot)));

        // 2.技能执行
        ExecuteStep skillExecuteStep = generateSkillExecuteStep();
        skillExecuteStep.setSkipFunction(planContext -> {
            ExecuteStep skillMatchTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_MATCH.getType())).findFirst().get();
            ExecuteResult executeResult = skillMatchTmpSubStep.getExecuteResult();
            return skillMatchTmpSubStep.isSkip() || BooleanUtils.isNotTrue(executeResult.isRecall());
        });
        ExecutionStep executeSkill = new ExecutionStep();
        executeSkill.setSteps(Lists.newArrayList(skillExecuteStep));

        // 3.大模型输出
        ExecuteStep llmStep = new ExecuteStep();
        llmStep.setType(BotExecutorTypeEnum.ANSWER.getType());
        ExecutableLLMParam llmParam = new ExecutableLLMParam();
        llmParam.setFillPromptFunction(((step, planContext) -> {
            ExecuteStep skillExecuteTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_DIRECT.getType())).findFirst().get();
            if (skillExecuteAbnormal(skillExecuteTmpSubStep)) {
                String characterPrompt = Optional.ofNullable(BotPromptUtil.promptPoundAdjust(planContext.getBot().getCharacterPrompt())).orElse(DEFAULT_PROMPT);
                characterPrompt = BotPromptUtil.fillPrompt(characterPrompt, planContext.getRunParam());
                return BotPromptUtil.fillPrompt(characterPrompt, Collections.emptyMap(), true);
            }
            FuncExecutionResultMessage message = new FuncExecutionResultMessage(skillExecuteTmpSubStep.getExecuteTool(), GsonUtil.toJson(skillExecuteTmpSubStep.getExecuteResult().getResult()));
            return GsonUtil.toJson(message);
        }));
        llmParam.setConvertFunctionResult(planContext -> {
            ExecuteStep skillExecuteTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_DIRECT.getType())).findFirst().get();
            return !skillExecuteTmpSubStep.isSkip();
        });
        Optional.ofNullable(bot.getModelConfig()).ifPresent(botModelConfig -> {
            Optional.ofNullable(botModelConfig.getBotPlanModel()).ifPresent(llmParam::setFridayModelConfig);
            Optional.ofNullable(getAppId(botModelConfig.getAppId())).ifPresent(llmParam::setAppId);
        });
        llmStep.setCustomParam(llmParam);
        ExecutionStep llmReply = new ExecutionStep();
        llmReply.setSteps(Lists.newArrayList(llmStep));

        tagPlan.setExecutionSteps(Lists.newArrayList(skillMatch, executeSkill, llmReply));

        return tagPlan;
    }

    public ExecutePlan generateRagPlan(AIBot bot) {
        ExecutePlan ragPlan = new ExecutePlan();
        ragPlan.setName(RAG_PLAN);

        // 1.知识库召回
        ExecuteStep knowledgeRecallStep = new ExecuteStep();
        knowledgeRecallStep.setType(BotExecutorTypeEnum.KNOWLEDGE.getType());
        ExecutionStep knowledgeRecall = new ExecutionStep();
        knowledgeRecall.setSteps(Lists.newArrayList(knowledgeRecallStep));
        // 2.大模型
        ExecuteStep llmReplyStep = new ExecuteStep();
        llmReplyStep.setType(BotExecutorTypeEnum.ANSWER.getType());
        ExecutableLLMParam llmParam = new ExecutableLLMParam();
        llmParam.setFillPromptFunction((step, planContext) -> {
            String characterPrompt = Optional.ofNullable(BotPromptUtil.promptPoundAdjust(planContext.getBot().getCharacterPrompt())).orElse(DEFAULT_PROMPT);
            characterPrompt = BotPromptUtil.fillPrompt(characterPrompt, planContext.getRunParam());
            // 知识库结果
            ExecuteStep knowledgeRecallTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.KNOWLEDGE.getType())).findFirst().get();
            BotKnowledgeRecallResult recallResult = (BotKnowledgeRecallResult) knowledgeRecallTmpSubStep.getExecuteResult().getResult().get(ExecutableKnowledgeRecall.DATA_FIELD);
            return BotPromptUtil.fillPrompt(characterPrompt, recallResult.getPlaceHolderAndContent(), true);
        });
        Optional.ofNullable(bot.getModelConfig()).ifPresent(configEntity -> {
            Optional.ofNullable(configEntity.getRagModel()).ifPresent(llmParam::setFridayModelConfig);
            Optional.ofNullable(getAppId(configEntity.getAppId())).ifPresent(llmParam::setAppId);
        });
        llmReplyStep.setCustomParam(llmParam);
        ExecutionStep llmReply = new ExecutionStep();
        llmReply.setSteps(Lists.newArrayList(llmReplyStep));

        ragPlan.setExecutionSteps(Lists.newArrayList(knowledgeRecall, llmReply));
        return ragPlan;
    }

    private ExecuteStep generateSkillExecuteStep() {
        ExecuteStep skillExecuteStep = new ExecuteStep();
        skillExecuteStep.setType(BotExecutorTypeEnum.FUNCTION_DIRECT.getType());
        ExecutableExecuteSkillParam executeSkillParam = new ExecutableExecuteSkillParam();
        executeSkillParam.setSkillFunction((step, planContext) -> {
            AIBot contextBot = planContext.getBot();
            ExecuteStep skillMatchTmpSubStep = planContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_MATCH.getType())).findFirst().get();
            ExecuteResult executeResult = skillMatchTmpSubStep.getExecuteResult();

            String functionName = (String) executeResult.getResult().get(ExecutableMatchSkill.FUNCTION_NAME_FIELD);
            step.setExecuteTool(functionName);
            List<Function> functions = contextBot.getPlugins().stream().flatMap(item -> item.getFunctionList().stream()).collect(Collectors.toList());
            for (Function function : functions) {
                if (!StringUtils.equals(function.getName(), functionName)) {
                    continue;
                }
                SpecifySkill specifySkill = new SpecifySkill();
                if (function.getFuncType() == FunctionTypeEnum.WORK_FLOW) {
                    specifySkill.setFlowId(Long.parseLong(function.getFlowId()));
                } else {
                    specifySkill.setFunctionId(function.getId());
                }
                return specifySkill;
            }
            throw new RuntimeException("找不到函数：" + functionName);
        });
        executeSkillParam.setExecuteParamFunction(executePlanContext -> {
            ExecuteStep skillMatchTmpSubStep = executePlanContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_MATCH.getType())).findFirst().get();
            ExecuteResult executeResult = skillMatchTmpSubStep.getExecuteResult();
            FunctionCallParamDto callParamDto = (FunctionCallParamDto) executeResult.getResult().get(ExecutableMatchSkill.ARGUMENTS_FIELD);
            return FunctionCallParamDto.convertParam(callParamDto.getParams());
        });
        executeSkillParam.setHeaderParamFunction(executePlanContext -> {
            ExecuteStep skillMatchTmpSubStep = executePlanContext.getExecutionSteps().stream().flatMap(item -> item.getSteps().stream()).filter(item -> StringUtils.equals(item.getType(), BotExecutorTypeEnum.FUNCTION_MATCH.getType())).findFirst().get();
            ExecuteResult executeResult = skillMatchTmpSubStep.getExecuteResult();
            FunctionCallParamDto callParamDto = (FunctionCallParamDto) executeResult.getResult().get(ExecutableMatchSkill.ARGUMENTS_FIELD);
            return FunctionCallParamDto.convertParam(callParamDto.getHeaders());
        });
        skillExecuteStep.setCustomParam(executeSkillParam);
        return skillExecuteStep;
    }

    private ExecuteStep generateSkillMatchStep(BotRunParam runParam, AIBot bot) {
        ExecuteStep skillMatchStep = new ExecuteStep();
        skillMatchStep.setType(BotExecutorTypeEnum.FUNCTION_MATCH.getType());
        ExecutableMatchSkillParam matchSkillParam = new ExecutableMatchSkillParam();
        matchSkillParam.setFillPromptFunction(((step, planContext) -> {
            String minorPrompt = Optional.ofNullable(BotPromptUtil.promptPoundAdjust(bot.getMinorPrompt())).orElse(DEFAULT_PROMPT);
            return BotPromptUtil.fillPrompt(minorPrompt, runParam);
        }));
        Optional.ofNullable(bot.getModelConfig()).ifPresent(botModelConfig -> {
            Optional.ofNullable(botModelConfig.getBotPlanModel()).ifPresent(matchSkillParam::setFridayModelConfig);
            Optional.ofNullable(getAppId(botModelConfig.getAppId())).ifPresent(matchSkillParam::setAppId);
        });
        skillMatchStep.setCustomParam(matchSkillParam);
        return skillMatchStep;
    }

    /**
     * 技能执行是否异常
     */
    private boolean skillExecuteAbnormal(ExecuteStep skillExecuteTmpSubStep) {
        return skillExecuteTmpSubStep.isSkip() || Objects.isNull(skillExecuteTmpSubStep.getExecuteResult()) || StringUtils.isBlank(skillExecuteTmpSubStep.getExecuteTool())
                || StringUtils.equals(FunctionDirectStreamHandler.DEFAULT_NAME, skillExecuteTmpSubStep.getExecuteTool())
                || MapUtils.isEmpty(skillExecuteTmpSubStep.getExecuteResult().getResult());
    }

    /**
     * 知识库是否召回正常
     */
    private boolean knowledgeBaseRecallNormal(ExecuteStep knowledgeRecallTmpSubStep) {
        ExecuteResult executeResult = knowledgeRecallTmpSubStep.getExecuteResult();
        BotKnowledgeRecallResult recallResult = (BotKnowledgeRecallResult) executeResult.getResult().get(ExecutableKnowledgeRecall.DATA_FIELD);
        return Objects.nonNull(recallResult) && executeResult.isRecall() && CollectionUtils.isEmpty(recallResult.getProcessCodes()) && MapUtils.isNotEmpty(recallResult.getPlaceHolderAndContent());
    }

    private String getAppId(String appId) {
        if (StringUtils.isBlank(appId)) {
            return null;
        }
        return appId;
    }

}
