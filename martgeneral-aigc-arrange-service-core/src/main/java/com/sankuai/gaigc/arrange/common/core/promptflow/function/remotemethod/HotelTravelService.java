package com.sankuai.gaigc.arrange.common.core.promptflow.function.remotemethod;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.gaigc.arrange.common.exception.RemoteServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/8 15:48
 */
@Slf4j
@Service
public class HotelTravelService {
    @MdpThriftClient(remoteAppKey = "com.sankuai.travel.quark", remoteServerPort = 9511, timeout = 1000)
    private com.meituan.travel.tdc.quark.service.PoiExtendService.Iface PoiExtendService;

    public Map<Long, Map<Integer, String>> getHotelPoiExtendValue(Long poiId, Integer attrId) {
        List<Long> poiIds = new ArrayList<>();
        poiIds.add(poiId);

        List<Integer> attrIds = new ArrayList<>();
        attrIds.add(attrId);
        try {
            Map<Long, Map<Integer, String>> result = PoiExtendService.getSomePoiExtendValue(poiIds,attrIds);
            log.info("HotelTravelService getHotelPoiExtendValue属性信息 请求成功, result:{}", result);
            return result;
        } catch (Exception e) {
            log.error("HotelTravelService getHotelPoiExtendValue属性信息出错, poiId:{},attrId:{},e=",poiId,attrId, e);
            throw new RemoteServiceException("PoiExtendService getSomePoiExtendValue属性信息出错, e=", e);
        }
    }


    public Map<Long, Map<Integer, String>> getHotelPoiExtendValues(Long poiId, List<Integer> attrIds) {
        List<Long> poiIds = new ArrayList<>();
        poiIds.add(poiId);

        try {
            Map<Long, Map<Integer, String>> result = PoiExtendService.getSomePoiExtendValue(poiIds,attrIds);
            log.info("HotelTravelService getHotelPoiExtendValues属性信息 请求成功, result:{}", result);
            return result;
        } catch (Exception e) {
            log.error("HotelTravelService getHotelPoiExtendValues属性信息出错, poiId:{},attrIds:{},e=",poiId, JSONObject.toJSONString(attrIds), e);
            throw new RemoteServiceException("HotelTravelService getHotelPoiExtendValues属性信息出错, e=", e);
        }
    }

}
