package com.sankuai.gaigc.arrange.common.core.audio.remote.s3;

import com.amazonaws.services.s3.AmazonS3;
import com.sankuai.gaigc.arrange.common.core.audio.utils.Md5CalculateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;


@Slf4j
@Component
public class AmazonS3ServiceImpl implements IAmazonS3Service {

    public static final String BUCKET_HOTEL_VIDEO = "bot-audio-file";

    @Autowired
    private AmazonS3 amazonS3;

    @Value("${mdp.s3plus[0].hostName}")
    private String hostName;

    /**
     * 合并文件目录
     */
    private static final String fileDirPath = "/opt/logs/audio/merged";

    /**
     * 文件地址拼接
     * http:/host/v1/${bucket}/${object}
     */
    private static final String RESOURCE_URL_TEMPLATE = "https://%s/%s/%s";


    @Override
    public void putObject(File file, String objectName, String bucketName) {
        try {
            amazonS3.putObject(bucketName, objectName, file);
        } catch (Exception e) {
            log.error("文件上传失败！objectName={}, e=", objectName, e);
            throw new RuntimeException("文件上传失败！");
        }
    }

    @Override
    public String uploadAudio(String audioFilePath) {
        //上传S3
        File audioFile = new File(audioFilePath);
        if (!audioFile.exists()) {
            log.error("文字转语音异常,文件不存在");
            throw new RuntimeException("文字转语音异常,文件不存在");
        }
        int dotIndex = audioFilePath.lastIndexOf('.');
        String fileSuffix = audioFilePath.substring(dotIndex + 1);

        String objectName = Md5CalculateUtils.getMD5(audioFile) + "." + fileSuffix;
        putObject(audioFile, objectName, BUCKET_HOTEL_VIDEO);

        audioFile.delete();

        return getObjectUrl(BUCKET_HOTEL_VIDEO, objectName);
    }


    @Override
    public String uploadAudio(File audioFile) {
        //上传S3
        if (!audioFile.exists()) {
            log.error("文字转语音异常,文件不存在");
            throw new RuntimeException("文字转语音异常,文件不存在");
        }
        String fileName = audioFile.getName();
        int dotIndex = fileName.lastIndexOf('.');
        String fileSuffix = fileName.substring(dotIndex + 1);

        String objectName = Md5CalculateUtils.getMD5(audioFile) + "." + fileSuffix;
        putObject(audioFile, objectName, BUCKET_HOTEL_VIDEO);

        audioFile.delete();

        return getObjectUrl(BUCKET_HOTEL_VIDEO, objectName);
    }


    @Override
    public String getObjectUrl(String bucket, String objectName) {
        return String.format(RESOURCE_URL_TEMPLATE, hostName, bucket, objectName);
    }



}
