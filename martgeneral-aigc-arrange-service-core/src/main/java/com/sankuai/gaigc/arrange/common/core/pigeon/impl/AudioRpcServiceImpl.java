package com.sankuai.gaigc.arrange.common.core.pigeon.impl;

import com.alibaba.fastjson.JSON;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.gaigc.arrange.api.entity.audio.AudioVO;
import com.sankuai.gaigc.arrange.api.entity.audio.TTSRequest;
import com.sankuai.gaigc.arrange.api.entity.audio.TTSResult;
import com.sankuai.gaigc.arrange.api.entity.common.Response;
import com.sankuai.gaigc.arrange.api.service.AudioRpcService;
import com.sankuai.gaigc.arrange.common.core.audio.entity.AudioEntity;
import com.sankuai.gaigc.arrange.common.core.audio.service.AudioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;


@MdpPigeonServer
@Slf4j
public class AudioRpcServiceImpl implements AudioRpcService {

    @Autowired
    private AudioService audioService;


    @Override
    public Response<TTSResult> text2Audio(TTSRequest ttsRequest) {
        try {
            return Response.success(audioService.text2Audio(ttsRequest));
        }catch (Exception e){
            log.error("text2Audio error, ttsRequest:{}", JSON.toJSONString(ttsRequest), e);
            return Response.failure(e.getMessage());
        }
    }

    @Override
    public Response<List<AudioVO>> getOnLineAudioList() {
        try {
            List<AudioEntity> audioList = audioService.getOnLineAudioList();
            return Response.success(audioList.stream().map(this::toVO).collect(Collectors.toList()));
        }catch (Exception e){
            log.error("getOnLineAudioList error", e);
            return Response.failure(e.getMessage());
        }
    }

    private AudioVO toVO(AudioEntity audioDO){
        if (audioDO == null){
            return null;
        }

        AudioVO vo = new AudioVO();
        vo.setAudioId(audioDO.getAudioId());
        vo.setSource(audioDO.getSource().getSourceId());
        vo.setName(audioDO.getName());
        vo.setVoice(audioDO.getVoice());
        vo.setDesc(audioDO.getDesc());
        vo.setDemoUrl(audioDO.getDemoUrl());
        vo.setCoverImgUrl(audioDO.getCoverImgUrl());
        return vo;
    }
}
