/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension;

import com.meituan.nibhtp.os.htp.cms.access.common.search.Expression;
import lombok.Data;

/**
 * 召回扩展配置
 *
 * <AUTHOR>
 * @created 2024/10/13
 */
@Data
public class BotKnowledgeBaseRecallExtConfig {
    /** 召回模式，1：标准文本，2：问答对 */
    private Integer recallMode = 1;
    /** 当召回模式=问答对时，问题过滤表达式 */
    private Expression questionExp;
    /** 当召回模式=问答对时，答案过滤表达式 */
    private Expression answerExp;
}