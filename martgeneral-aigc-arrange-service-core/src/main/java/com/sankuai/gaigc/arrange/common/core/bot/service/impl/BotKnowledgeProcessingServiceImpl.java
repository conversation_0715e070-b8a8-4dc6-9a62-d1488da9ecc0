/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeBaseItem;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.FieldProcessingItemConfig;
import com.sankuai.gaigc.arrange.common.core.bot.enums.FormatStrategyEnum;
import com.sankuai.gaigc.arrange.common.core.bot.service.BotKnowledgeProcessingService;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeResultItem;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 知识加工服务
 *
 * <AUTHOR>
 * @created 2024/6/3
 */
@Slf4j
@Service
public class BotKnowledgeProcessingServiceImpl implements BotKnowledgeProcessingService {

    @Override
    public Map<String, String> processingKnowledge(Map<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseIdAndRetrieveResult, Map<Long, BotKnowledgeBaseItem> knowledgeBaseAndConfig) {
        if (MapUtils.isEmpty(knowledgeBaseIdAndRetrieveResult)) {
            return Maps.newHashMap();
        }

        Map<String, List<String>> phAndKnowledge = Maps.newHashMap();
        for (Map.Entry<Long, Map<String, KnowledgeBaseRetrieveResult>> knowledgeBaseEntry : knowledgeBaseIdAndRetrieveResult.entrySet()) {
            Long knowledgeBaseId = knowledgeBaseEntry.getKey();
            Map<String, KnowledgeBaseRetrieveResult> resultMap = knowledgeBaseEntry.getValue();
            BotKnowledgeBaseItem knowledgeBaseConfig = knowledgeBaseAndConfig.get(knowledgeBaseId);

            Map<String, List<String>> phAndContentMap = processingKnowledge(resultMap, knowledgeBaseConfig);
            phAndContentMap.forEach((ph, content) -> {
                phAndKnowledge.merge(ph, content, (o, n) -> {
                    o.addAll(n);
                    return o;
                });
            });
        }
        return phAndKnowledge.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> StringUtils.join(entry.getValue(), SymbolConstant.NEW_LINE)));
    }

    private Map<String, List<String>> processingKnowledge(Map<String, KnowledgeBaseRetrieveResult> resultMap, BotKnowledgeBaseItem knowledgeBaseConfig) {
        Map<String, List<String>> phAndContentMap = Maps.newHashMap();

        Map<String, FieldProcessingItemConfig> fieldProcessConfig = getFieldProcessConfig(knowledgeBaseConfig);
        for (Map.Entry<String, KnowledgeBaseRetrieveResult> resultEntry : resultMap.entrySet()) {
            phAndContentMap.put(resultEntry.getKey(), processingKnowledge(resultEntry.getValue(), fieldProcessConfig));
        }
        return phAndContentMap;
    }

    private List<String> processingKnowledge(KnowledgeBaseRetrieveResult retrieveResult, Map<String, FieldProcessingItemConfig> fieldProcessConfig) {
        if (Objects.isNull(retrieveResult)) {
            return Lists.newArrayList();
        }

        List<String> items = Lists.newArrayList();
        for (KnowledgeResultItem resultItem : retrieveResult.getResult()) {
            if (MapUtils.isEmpty(fieldProcessConfig)) {
                items.add(GsonUtil.toJson(resultItem.getFieldData()));
                continue;
            }
            for (Map.Entry<String, Object> entry : resultItem.getFieldData().entrySet()) {
                String fieldName = entry.getKey();
                FieldProcessingItemConfig config = fieldProcessConfig.get(fieldName);
                if (Objects.isNull(config) || Objects.isNull(entry.getValue())) {
                    continue;
                }
                items.add(fieldToString(entry.getValue().toString(), config));
            }
        }
        return items;
    }

    private String fieldToString(String fieldValue, FieldProcessingItemConfig config) {
        return Optional.ofNullable(config.getPrefix()).orElse(SymbolConstant.EMPTY)
                + formatFieldValue(fieldValue, config)
                + Optional.ofNullable(config.getSuffix()).orElse(SymbolConstant.EMPTY);
    }

    private Map<String, FieldProcessingItemConfig> getFieldProcessConfig(BotKnowledgeBaseItem knowledgeBaseConfig) {
        if (Objects.isNull(knowledgeBaseConfig) || CollectionUtils.isEmpty(knowledgeBaseConfig.getProcessConfig())) {
            return Maps.newHashMap();
        }
        return knowledgeBaseConfig.getProcessConfig().stream().collect(Collectors.toMap(FieldProcessingItemConfig::getFieldName, Function.identity()));
    }

    private String formatFieldValue(String fieldValue, FieldProcessingItemConfig config) {
        if (Objects.isNull(fieldValue)) {
            return SymbolConstant.EMPTY;
        }

        FormatStrategyEnum strategyEnum = FormatStrategyEnum.getEnumByValue(config.getFormatStrategy());
        switch (strategyEnum) {
            case JSON_LIST_FORMAT:
                List<String> list = GsonUtil.fromJson(fieldValue, new TypeToken<List<String>>() {
                }.getType());
                if (CollectionUtils.isEmpty(list)) {
                    return SymbolConstant.EMPTY;
                }
                return String.join(SymbolConstant.EMPTY, list);
            default:
                return fieldValue;
        }
    }

}
