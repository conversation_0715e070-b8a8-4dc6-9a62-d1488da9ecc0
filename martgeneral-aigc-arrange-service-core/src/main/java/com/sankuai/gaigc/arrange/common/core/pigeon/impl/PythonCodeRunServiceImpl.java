/*
 * Copyright (c) 2025 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.pigeon.impl;

import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.entity.common.Response;
import com.sankuai.gaigc.arrange.api.entity.python.PythonCodeRunRequest;
import com.sankuai.gaigc.arrange.api.entity.python.PythonCodeRunResult;
import com.sankuai.gaigc.arrange.api.enums.FlowRunModeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.service.PythonCodeRunService;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.CompParamsUtil;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.PythonScript;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.proxy.IComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * Python代码运行服务
 *
 * <AUTHOR>
 * @created 2025/2/22
 */
@Slf4j
@MdpPigeonServer
public class PythonCodeRunServiceImpl implements PythonCodeRunService {
    private static final String PYTHON_NONE = "None";
    @Resource
    private IComponent pythonScript;

    @Override
    public Response<PythonCodeRunResult> runCode(PythonCodeRunRequest request) {
        PythonCodeRunResult codeRunResult = new PythonCodeRunResult();
        if (StringUtils.isBlank(request.getCode())) {
            codeRunResult.setStdData(SymbolConstant.EMPTY);
            codeRunResult.setFrontData(SymbolConstant.EMPTY);
            return Response.success(codeRunResult);
        }

        Map<String, Object> inputs = Maps.newHashMap();
        if (StringUtils.isNotBlank(request.getInputs())) {
            inputs = GsonUtil.fromJson(request.getInputs(), new TypeToken<Map<String, Object>>() {
            }.getType());
        }
        ComponentInfo componentInfo = new ComponentInfo();
        componentInfo.setAppId(String.valueOf(request.getAppId()));
        componentInfo.setComponentName(PythonScript.class.getSimpleName());
        Map<String, Object[]> paramInfo = Maps.newHashMap();
        paramInfo.put("code", new Object[]{request.getCode(), ParamTypeEnum.STRING});
        paramInfo.put("inputs", new Object[]{inputs, ParamTypeEnum.MAP});
        componentInfo.setInputs(CompParamsUtil.buildInput(paramInfo));
        Context componentContext = new Context();
        componentContext.setRunMode(FlowRunModeEnum.SYNC);
        try {
            Map<String, Object> result = (Map<String, Object>) pythonScript.execute(componentContext, componentInfo);
            Object resultData = result.get(PythonScript.COMPONENT_RESULT_FIELD);
            String str = toStr(resultData);
            codeRunResult.setStdData(str);
            codeRunResult.setFrontData(str);
        } catch (Exception e) {
            log.error("Python代码运行出错，request:{}, e=", request, e);
            codeRunResult.setStdError(e.getMessage());
        }
        return Response.success(codeRunResult);
    }

    private String toStr(Object resultData) {
        if (Objects.isNull(resultData)) {
            return PYTHON_NONE;
        }
        return AbstractComponent.extractValue(resultData);
    }
}