/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.plan.chain;

import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Bot流式处理器
 *
 * <AUTHOR>
 * @created 2024/8/16
 */
@Order(2)
@Component
@Slf4j
public class BotStreamProcessor implements IAgentAspectProcessor {

    @Override
    public void whenStart(BotRunParam runParam, AIBot bot) {

    }

    @Override
    public void whenError(BotRunParam runParam, AIBot bot, Exception executorError) {
        long start = bot.getStart() > 0 ? bot.getStart() : System.currentTimeMillis();
        double second = (double) (System.currentTimeMillis() - start) / 1000.0D;
        if (!StreamInfoHolder.availableStream(runParam.getStream())) {
            return;
        }

        try {
            SSEMessage sseMessage = SSEMessageUtils.generateAnswerErrorMessage(second, SSEMessageUtils.generateSectionId(), executorError);
            StreamInfoHolder.getEmitter().send(sseMessage);
        } catch (IOException e) {
            log.error("流式输出出错, botId:{}, e=", bot.getId(), e);
        }
    }

    @Override
    public void whenSuccess(BotRunResult runResult, BotRunParam runParam, AIBot bot) {
    }
}