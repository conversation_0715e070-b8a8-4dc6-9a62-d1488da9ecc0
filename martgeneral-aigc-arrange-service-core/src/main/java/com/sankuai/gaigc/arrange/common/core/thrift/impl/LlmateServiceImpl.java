package com.sankuai.gaigc.arrange.common.core.thrift.impl;

import com.meituan.service.mobile.mtthrift.util.ClientInfoUtil;
import com.sankuai.gaigc.arrange.api.enums.ErrorCodeEnum;
import com.sankuai.gaigc.arrange.api.enums.FlowRunModeEnum;
import com.sankuai.gaigc.arrange.api.thrift.LlmateRequest;
import com.sankuai.gaigc.arrange.api.thrift.LlmateResponse;
import com.sankuai.gaigc.arrange.api.thrift.LlmateService;
import com.sankuai.gaigc.arrange.api.thrift.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.abtest.AbTestRequest;
import com.sankuai.gaigc.arrange.common.core.promptflow.abtest.AbTestResponse;
import com.sankuai.gaigc.arrange.common.core.promptflow.abtest.HorizonAbTestService;
import com.sankuai.gaigc.arrange.common.core.promptflow.beans.PromptFlowManager;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ResponseConstants;
import com.sankuai.gaigc.arrange.common.core.promptflow.dsl.Flow;
import com.sankuai.gaigc.arrange.common.core.promptflow.engine.PromptFlowEngine;
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.AsyncRunFlowEntity;
import com.sankuai.gaigc.arrange.common.core.promptflow.enums.AsyncRunFlowStatusEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.exception.LLMUnavailableParamException;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.GrayPublishUtils;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.ThriftUtils;
import com.sankuai.gaigc.arrange.common.enums.AppEntityTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sankuai.gaigc.arrange.common.core.promptflow.util.ThriftUtils.*;

/**
 * @Author: wenhao10
 * @Description:
 * @Date: 2023-08-31 17:12
 */
@Slf4j
public class LlmateServiceImpl implements LlmateService.Iface {
    @Autowired
    PromptFlowEngine promptFlowEngine;
    @Autowired
    PromptFlowManager promptFlowManager;
    @Autowired
    HorizonAbTestService horizonAbTestService;

    @Override
    public LlmateResponse runFlow(LlmateRequest request) throws TException {
        AbTestResponse abTestResponse = executeAbTest(request);
        Flow flow = determineFlow(request, abTestResponse);
        Map<String, Param> thriftInputParams = request.getInputParams();
        Map<String, Object> inputParams = null;
        if (thriftInputParams != null) {
            inputParams = thriftInputParams.entrySet().stream().collect(HashMap::new, (hashMap, entry) -> hashMap.put(entry.getKey(), ThriftUtils.parseThriftParam(entry.getValue())), HashMap::putAll);
        }
        Map<String, Param> thriftBizParams = request.getBizParams();
        Map<String, Object> envMap = null;
        if (thriftBizParams != null) {
            Map<String, Object> bizParams = thriftBizParams.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> ThriftUtils.parseThriftParam(e.getValue())));
            envMap = new HashMap<>();
            envMap.put("bizParams", bizParams);
        }
        FlowRunModeEnum flowRunModeEnum = FlowRunModeEnum.valueOf(request.getRunMode());
        if (FlowRunModeEnum.ASYNC == flowRunModeEnum) {
            return buildAsyncLlmateResponse(
                    (String) promptFlowEngine.runFlowAsync(flow, inputParams, envMap, ClientInfoUtil.getClientAppKey())
                            .get(ResponseConstants.TASK_ID)
            );
        }
        try {
            return buildSuccessResponse(promptFlowEngine.runFlow(flow, inputParams, envMap, flowRunModeEnum));
        } catch (LLMUnavailableParamException e) {
            LLMUnavailableParamException cause = (LLMUnavailableParamException) e.getCause();
            log.error("LLMUnavailableParamException caught with error code: {}, message: {}, request: {}", e.getErrorCode(), e.getMessage(), request, e);
            return buildFailedResponse(cause.getErrorCode(), cause.getMessage());
        }
    }

    @Override
    public LlmateResponse getRunFlowAsyncResult(String taskId) throws TException {
        AsyncRunFlowEntity runFlowAsyncResult = promptFlowEngine.getRunFlowAsyncResult(taskId);
        if (Objects.isNull(runFlowAsyncResult)) {
            return ThriftUtils.buildFailedResponse(ErrorCodeEnum.ASYNC_RESULT_NOT_FOUND, ErrorCodeEnum.ASYNC_RESULT_NOT_FOUND.getDesc());
        }
        AsyncRunFlowStatusEnum status = AsyncRunFlowStatusEnum.valueOf(runFlowAsyncResult.getStatus());
        if (AsyncRunFlowStatusEnum.ERROR == status) {
            return ThriftUtils.buildFailedResponse(ErrorCodeEnum.INNER_ERROR, runFlowAsyncResult.getMsg());
        }
        if (AsyncRunFlowStatusEnum.RUNNING == status) {
            return ThriftUtils.buildFailedResponse(ErrorCodeEnum.ASYNC_FLOW_RUNNING, ErrorCodeEnum.ASYNC_FLOW_RUNNING.getDesc());
        }
        return buildSuccessResponse(runFlowAsyncResult.getRunFlowResponse(), runFlowAsyncResult.getTraceId());
    }

    private AbTestResponse executeAbTest(LlmateRequest request) {
        AbTestRequest abTestRequest = AbTestRequest.builder()
                .flowId(request.getFlowId())
                .abTestParam(request.getAbTestParam())
                .build();
        return horizonAbTestService.executeAbTest(abTestRequest);
    }

    private Flow determineFlow(LlmateRequest request, AbTestResponse abTestResponse) {
        if (abTestResponse.isDoAbTest()) {
            return promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(request.getFlowId(), abTestResponse.getVersion());
        } else {
            //250218 增加“灰度发布”判断 如果正在灰度中并命中 则用灰度版本替换
            if (request.getVersion() == 0) {
                Integer grayVersion = GrayPublishUtils.getGrayPublishVersion(AppEntityTypeEnum.FLOW, request.getFlowId());
                if (grayVersion != null) {
                    return promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(request.getFlowId(), grayVersion);
                }
            }
            return request.getVersion() == 0 ? promptFlowManager.getFlowByIdIfItIsInTheCache(request.getFlowId())
                    : promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(request.getFlowId(), request.getVersion());
        }
    }
}
