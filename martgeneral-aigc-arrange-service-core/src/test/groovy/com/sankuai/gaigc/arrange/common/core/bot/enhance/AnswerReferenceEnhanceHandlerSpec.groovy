package com.sankuai.gaigc.arrange.common.core.bot.enhance

import com.google.common.collect.Lists
import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.AnswerReferenceEnhance
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.AnswerReferenceKnowledge
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.BotEnhanceItem
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult
import com.sankuai.gaigc.arrange.common.core.bot.enhance.success.AnswerReferenceEnhanceHandler
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlan
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService
import com.sankuai.gaigc.arrange.common.util.VectorUtil
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil
import com.sankuai.gaigc.arrange.config.MccConfig
import com.sankuai.gaigc.arrange.remote.basicapi.ContentEmbeddingRemoteService
import org.apache.commons.collections.CollectionUtils
import org.assertj.core.util.Sets
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import static org.powermock.api.mockito.PowerMockito.mockStatic
import static org.powermock.api.mockito.PowerMockito.when

/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest(VectorUtil.class)
class AnswerReferenceEnhanceHandlerSpec extends Specification {
    AnswerReferenceEnhanceHandler answerReferenceEnhanceHandler
    AIBotKnowledgeBaseService knowledgeBaseService
    MccConfig mccConfig
    ContentEmbeddingRemoteService contentEmbeddingRemoteService;

    def setup() {
        knowledgeBaseService = Mock()
        contentEmbeddingRemoteService = Mock()
        mccConfig = Mock()
        answerReferenceEnhanceHandler = new AnswerReferenceEnhanceHandler(knowledgeBaseService: knowledgeBaseService
                , contentEmbeddingRemoteService: contentEmbeddingRemoteService
                , mccConfig: mccConfig)
    }

    @Unroll
    def "generateAnswerReferenceSuccess"() {
        given:
        String enhanceConfigJson = "{\"score\":0.95,\"topK\":3,\"id\":1,\"open\":true,\"className\":\"AnswerReferenceEnhance\"}"
        BotEnhanceItem enhanceConfig = GsonUtil.fromJson(enhanceConfigJson, AnswerReferenceEnhance.class)
        String runResultJson = "{\"success\":true,\"message\":null,\"replyContent\":\"消费者优惠券额度由平台补贴和商家补贴构成。不同消费者所领取到的“酒店神券”活动优惠券额度可能不同，优惠券额度内的平台补贴额度也会不同，因此不同订单的“酒店神券”活动商家补贴成本可能不同，补贴算法逻辑无法透露。\",\"result\":{\"answer\":\"消费者优惠券额度由平台补贴和商家补贴构成。不同消费者所领取到的“酒店神券”活动优惠券额度可能不同，优惠券额度内的平台补贴额度也会不同，因此不同订单的“酒店神券”活动商家补贴成本可能不同，补贴算法逻辑无法透露。\"},\"processCode\":[],\"cards\":[],\"recall\":true,\"referenceKnowledge\":null,\"plan\":{\"name\":\"RAGCall\",\"steps\":[],\"executionSteps\":[{\"stepNo\":1,\"status\":\"SUCCESS\",\"steps\":[{\"step\":0,\"type\":\"knowledge\",\"objective\":null,\"executeTool\":null,\"sectionId\":\"8667594967066823077\",\"stepNo\":1,\"startTime\":1724294649956,\"executeResult\":{\"success\":true,\"objective\":null,\"result\":{},\"message\":\"\",\"recall\":true,\"processCodes\":[]},\"status\":\"SUCCESS\",\"skip\":false,\"hitCard\":false,\"customParam\":null,\"skipFunction\":null}]}],\"objective\":null,\"recall\":true}}"
        BotRunResult runResult = GsonUtil.fromJson(runResultJson, BotRunResult.class)
        String knowledgeRecallResultJson = "{\"originRecallResult\":[{\"knowledgeBaseId\":10,\"knowledgeBaseName\":\"住宿知识库中台\",\"result\":[{\"fieldData\":{\"staticKnowledgeId\":3,\"qaId\":9014212,\"question\":\"神会员的商家想要开白名单并且要免费使用一段试点怎么操作?\",\"answer\":\"如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\"},\"maxScore\":0.8265024},{\"fieldData\":{\"staticKnowledgeId\":3,\"qaId\":31926,\"question\":\"“酒店神券”活动的促销金额计算规则是什么？\",\"answer\":\"消费者优惠券额度由平台补贴和商家补贴构成。不同消费者所领取到的“酒店神券”活动优惠券额度可能不同，优惠券额度内的平台补贴额度也会不同，因此不同订单的“酒店神券”活动商家补贴成本可能不同，补贴算法逻辑无法透露。\"},\"maxScore\":0.82858074}]}],\"placeHolderAndContent\":{\"knowledgeByQuestion\":\"问题：神会员的商家想要开白名单并且要免费使用一段试点怎么操作?；\\n答案：如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。\\n如图所示https://p0.meituan.net/travelcube/a94d137382a8e1480af6b73f292b7d8230560.png；故因广告主的个人原因产生的退款需求无法支持。；\\n问题：“酒店神券”活动的促销金额计算规则是什么？；\\n答案：消费者优惠券额度由平台补贴和商家补贴构成。不同消费者所领取到的“酒店神券”活动优惠券额度可能不同，优惠券额度内的平台补贴额度也会不同，因此不同订单的“酒店神券”活动商家补贴成本可能不同，补贴算法逻辑无法透露。；\",\"knowledgeByDocument\":\"\"},\"processCodes\":[]}"
        BotKnowledgeRecallResult knowledgeRecallResult = GsonUtil.fromJson(knowledgeRecallResultJson, BotKnowledgeRecallResult.class)
        String planJson = "{\"name\":\"RAGCall\",\"steps\":[],\"executionSteps\":[{\"stepNo\":1,\"status\":\"SUCCESS\",\"steps\":[{\"step\":0,\"type\":\"knowledge\",\"objective\":null,\"executeTool\":null,\"sectionId\":\"8667594967066823077\",\"stepNo\":1,\"startTime\":1724294649956,\"executeResult\":{\"success\":true,\"objective\":null,\"result\":{},\"message\":\"\",\"recall\":true,\"processCodes\":[]},\"status\":\"SUCCESS\",\"skip\":false,\"hitCard\":false,\"customParam\":null,\"skipFunction\":null}]}],\"objective\":null,\"recall\":true}"
        runResult.setPlan(GsonUtil.fromJson(planJson, ExecutePlan.class))
        runResult.getPlan().getExecutionSteps().get(0).getSteps().get(0).getExecuteResult().getResult().put("data", knowledgeRecallResult)
        String runParamJson = "{\"botId\":478,\"userMsg\":\"神会员的推广策略对商家的影响是什么？\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1},\"bizMsgHistory\":null,\"bizParams\":{\"identification\":\"FEXYOQ7T7P\",\"query\":\"神会员的推广策略对商家的影响是什么？\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1}},\"sessionId\":\"464b248a-6954-4013-ac1f-2836b17c997b\",\"historyMessages\":[],\"compressMessages\":[],\"debug\":false,\"stream\":true}"
        BotRunParam runParam = GsonUtil.fromJson(runParamJson, BotRunParam.class)
        AIBot aiBot = AIBot.builder().id(478).build()

        mccConfig.getKCubeKnowledgeBaseId() >> 10L

        contentEmbeddingRemoteService.textEmbeddingUseDouble(_, _) >> Lists.newArrayList(0.622F)

        mockStatic(VectorUtil)
        when(VectorUtil.cosineSimilarity(Mockito.anyList(), Mockito.anyList())).thenReturn(0.998D)

        List<KnowledgeBase> knowledgeBases = Lists.newArrayList()
        knowledgeBaseService.queryByIds(_) >> knowledgeBases
        when:
        answerReferenceEnhanceHandler.handle(enhanceConfig, runResult, runParam, aiBot)
        then:
        CollectionUtils.isNotEmpty(runResult.getEnhanceResult().getReferenceKnowledge())
        Set<String> set = Sets.newHashSet()
        for (AnswerReferenceKnowledge referenceKnowledge : runResult.getEnhanceResult().getReferenceKnowledge()) {
            set.add(referenceKnowledge.getKnowledgeContent())
        }
        set.contains("消费者优惠券额度由平台补贴和商家补贴构成。不同消费者所领取到的“酒店神券”活动优惠券额度可能不同，优惠券额度内的平台补贴额度也会不同，因此不同订单的“酒店神券”活动商家补贴成本可能不同，补贴算法逻辑无法透露。")
    }

}
