package com.sankuai.gaigc.arrange.common.core.bot.service.impl

import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotEnhanceService
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService
import com.sankuai.gaigc.arrange.common.core.bot.service.AiBotPluginService
import com.sankuai.gaigc.arrange.common.exception.NotExistException
import com.sankuai.gaigc.arrange.dao.dal.entity.AIBotDO
import com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO
import com.sankuai.gaigc.arrange.dao.dal.mapper.AIBotDOMapper
import com.sankuai.gaigc.arrange.dao.dal.mapper.AIBotSnapshotDOMapper
import spock.lang.Specification

class AIBotSnapshotServiceImplSpec extends Specification {
    AIBotSnapshotServiceImpl aiBotSnapshotService
    AIBotSnapshotDOMapper botSnapshotDOMapper
    AIBotDOMapper botDOMapper;
    AiBotPluginService botPluginService
    AIBotKnowledgeBaseService knowledgeBaseService
    AIBotEnhanceService aiBotEnhanceService

    def setup() {
        botSnapshotDOMapper = Mock(AIBotSnapshotDOMapper)
        botDOMapper = Mock(AIBotDOMapper)
        botPluginService = Mock(AiBotPluginService)
        knowledgeBaseService = Mock(AIBotKnowledgeBaseService)
        aiBotEnhanceService = Mock()

        aiBotSnapshotService = new AIBotSnapshotServiceImpl(botSnapshotDOMapper: botSnapshotDOMapper,
                botDOMapper: botDOMapper, botPluginService: botPluginService,
                knowledgeBaseService: knowledgeBaseService, aiBotEnhanceService: aiBotEnhanceService)
    }

    def "generateAIBot_selectAIBotNull"() {
        given:
        Long botId = 1L
        Integer version = 1
        botSnapshotDOMapper.selectByExampleWithBLOBs(_) >> null

        when:
        AIBot result = aiBotSnapshotService.generateAIBot(botId, version)

        then:
        thrown(NotExistException)
    }

    def "generateAIBot_selectPluginEmpty"() {
        given:
        Long botId = 1L
        Integer version = 1
        AIBotDO botDO = new AIBotDO(id: 1, name: "1")
        AIBotSnapshotDO botSnapshotDO = new AIBotSnapshotDO(
                id: 12,
                botId: 4,
                creator: "jiangbingbing03",
                characterPrompt: "角色\n你是在线酒店服务商OTA专家，可以为回答用户的各种问题。在酒店中，一个物理酒店称为poi，一个商品称为goods，用户可能问你任何有关poi或者goods的问题\n=====\n## 限制\n- 只讨论与酒店有关的内容，拒绝回答与酒店无关的话题。\n- 总结部分不能超过 100 字。\n- 请使用 Markdown 的 ^^ 形式说明引用来源。",
                plugins: "{\"1\":[\"4\",\"5\"]}",
                flows: "",
                knowledgeBases: "1",
                contextRound: 3,
                prologue: "我是小助手的prologue，你是谁？",
                fallbackReplySwitch: 0,
                fallbackReply: "我是小助手",
                version: 4,
                versionDesc: "小助手测试",
                status: 2
        )
        botSnapshotDOMapper.selectByExampleWithBLOBs(_) >> [botSnapshotDO]
        botDOMapper.selectByPrimaryKey(_) >> botDO
        botPluginService.queryPluginByIds(_) >> []
        botPluginService.queryDraftPluginByIds(_) >> []
        knowledgeBaseService.queryByIds(_) >> [new KnowledgeBase()]
        aiBotEnhanceService.getAll() >> []

        when:
        AIBot result = aiBotSnapshotService.generateAIBot(botId, version)

        then:
        result.id == botSnapshotDO.botId
        result.name == botDO.name
        result.version == botSnapshotDO.version.toString()
        result.plugins == []
        result.characterPrompt == botSnapshotDO.characterPrompt
        result.prologue == botSnapshotDO.prologue
        result.fallbackReply == botSnapshotDO.fallbackReply
        result.contextRound == botSnapshotDO.contextRound
    }


    def "generateAIBot_selectPluginEmptyV2"() {
        given:
        Long botId = 1L
        Integer version = 0
        AIBotDO botDO = new AIBotDO(id: 1, name: "1")
        AIBotSnapshotDO botSnapshotDO = new AIBotSnapshotDO(
                id: 12,
                botId: 4,
                creator: "jiangbingbing03",
                characterPrompt: "角色\n你是在线酒店服务商OTA专家，可以为回答用户的各种问题。在酒店中，一个物理酒店称为poi，一个商品称为goods，用户可能问你任何有关poi或者goods的问题\n=====\n## 限制\n- 只讨论与酒店有关的内容，拒绝回答与酒店无关的话题。\n- 总结部分不能超过 100 字。\n- 请使用 Markdown 的 ^^ 形式说明引用来源。",
                plugins: "{\"1\":[\"4\",\"5\"]}",
                flows: "",
                knowledgeBases: "1",
                contextRound: 3,
                prologue: "我是小助手的prologue，你是谁？",
                fallbackReplySwitch: 0,
                fallbackReply: "我是小助手",
                version: 0,
                versionDesc: "小助手测试",
                status: 2
        )
        botSnapshotDOMapper.selectByExampleWithBLOBs(_) >> [botSnapshotDO]
        botDOMapper.selectByPrimaryKey(_) >> botDO
        botPluginService.queryPluginByIds(_) >> []
        knowledgeBaseService.queryByIds(_) >> [new KnowledgeBase()]
        aiBotEnhanceService.getAll() >> []

        when:
        AIBot result = aiBotSnapshotService.generateAIBot(botId, version)

        then:
        result.id == botSnapshotDO.botId
        result.name == botDO.name
        result.version == botSnapshotDO.version.toString()
        result.plugins == []
        result.characterPrompt == botSnapshotDO.characterPrompt
        result.prologue == botSnapshotDO.prologue
        result.fallbackReply == botSnapshotDO.fallbackReply
        result.contextRound == botSnapshotDO.contextRound
    }


    def "generateAIBot_selectPluginEmptyV3"() {
        given:
        Long botId = 1L
        Integer version = null
        AIBotDO botDO = new AIBotDO(id: 1, name: "1")
        AIBotSnapshotDO botSnapshotDO = new AIBotSnapshotDO(
                id: 12,
                botId: 4,
                creator: "jiangbingbing03",
                characterPrompt: "角色\n你是在线酒店服务商OTA专家，可以为回答用户的各种问题。在酒店中，一个物理酒店称为poi，一个商品称为goods，用户可能问你任何有关poi或者goods的问题\n=====\n## 限制\n- 只讨论与酒店有关的内容，拒绝回答与酒店无关的话题。\n- 总结部分不能超过 100 字。\n- 请使用 Markdown 的 ^^ 形式说明引用来源。",
                plugins: "{\"1\":[\"4\",\"5\"]}",
                flows: "",
                knowledgeBases: "1",
                contextRound: 3,
                prologue: "我是小助手的prologue，你是谁？",
                fallbackReplySwitch: 0,
                fallbackReply: "我是小助手",
                version: 4,
                versionDesc: "小助手测试",
                status: 2
        )
        botSnapshotDOMapper.selectByExampleWithBLOBs(_) >> [botSnapshotDO]
        botDOMapper.selectByPrimaryKey(_) >> botDO
        botPluginService.queryPluginByIds(_) >> []
        knowledgeBaseService.queryByIds(_) >> [new KnowledgeBase()]
        aiBotEnhanceService.getAll() >> []

        when:
        AIBot result = aiBotSnapshotService.generateAIBot(botId, version)

        then:
        result.id == botSnapshotDO.botId
        result.name == botDO.name
        result.version == botSnapshotDO.version.toString()
        result.plugins == []
        result.characterPrompt == botSnapshotDO.characterPrompt
        result.prologue == botSnapshotDO.prologue
        result.fallbackReply == botSnapshotDO.fallbackReply
        result.contextRound == botSnapshotDO.contextRound
    }

    def "generateAIBot_versionNull"() {
        given:
        Long botId = 1L
        AIBotDO botDO = new AIBotDO(id: 1, name: "1")
        AIBotSnapshotDO botSnapshotDO = new AIBotSnapshotDO(
                id: 12,
                botId: 4,
                creator: "jiangbingbing03",
                characterPrompt: "角色\n你是在线酒店服务商OTA专家，可以为回答用户的各种问题。在酒店中，一个物理酒店称为poi，一个商品称为goods，用户可能问你任何有关poi或者goods的问题\n=====\n## 限制\n- 只讨论与酒店有关的内容，拒绝回答与酒店无关的话题。\n- 总结部分不能超过 100 字。\n- 请使用 Markdown 的 ^^ 形式说明引用来源。",
                plugins: "{\"1\":[\"4\",\"5\"]}",
                flows: "",
                knowledgeBases: "1",
                contextRound: 3,
                prologue: "我是小助手的prologue，你是谁？",
                fallbackReplySwitch: 0,
                fallbackReply: "我是小助手",
                version: 4,
                versionDesc: "小助手测试",
                status: 1
        )
        aiBotEnhanceService.getAll() >> []

        when:
        aiBotSnapshotService.generateAIBot(botId, null)

        then:
        1 * botDOMapper.selectByPrimaryKey(_) >> botDO
        1 * botSnapshotDOMapper.selectByExampleWithBLOBs(_) >> [botSnapshotDO]
        1 * botPluginService.queryPluginByIds(_) >> []
        1 * knowledgeBaseService.queryByIds(_) >> [new KnowledgeBase()]
    }


}
