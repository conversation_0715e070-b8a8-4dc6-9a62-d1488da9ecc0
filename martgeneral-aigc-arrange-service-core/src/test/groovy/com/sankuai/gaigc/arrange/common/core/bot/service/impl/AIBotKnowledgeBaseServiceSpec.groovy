/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.service.impl

import com.google.common.collect.Lists
import com.google.gson.reflect.TypeToken
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.KnowledgeBaseRetrieve
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseField
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseMappingField
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeStorageInfo
import com.sankuai.gaigc.arrange.dao.dal.entity.KnowledgeBaseDO
import com.sankuai.gaigc.arrange.dao.dal.enums.KnowledgeTypeEnum
import com.sankuai.gaigc.arrange.dao.dal.enums.VecBaseEnum
import com.sankuai.gaigc.arrange.dao.dal.mapper.KnowledgeBaseMapper
import com.sankuai.solarsystem.aigc.basicapi.client.enums.embedding.EmbeddingModelNameEnum
import spock.lang.Specification
import spock.lang.Unroll
/**
 * AIBotKnowledgeBaseServiceSpec
 *
 * <AUTHOR>
 * @created 2024/4/25
 */
class AIBotKnowledgeBaseServiceSpec extends Specification {
    AIBotKnowledgeBaseService knowledgeBaseService
    KnowledgeBaseMapper knowledgeBaseMapper
    KnowledgeBaseRetrieve knowledgeBaseRetrieve

    KnowledgeBase knowledgeBase
    KnowledgeBaseDO knowledgeBaseDO
    String resultJson

    def setup() {
        knowledgeBaseMapper = Mock()
        knowledgeBaseRetrieve = Mock()
        knowledgeBaseService = new AIBotKnowledgeBaseServiceImpl(knowledgeBaseMapper: knowledgeBaseMapper)

        knowledgeBase = new KnowledgeBase()
        knowledgeBase.setId(3L)
        knowledgeBase.setName("国内SA酒店语料知识库")
        knowledgeBase.setKnowledgeType(KnowledgeTypeEnum.TEXT)
        knowledgeBase.setVecBase(VecBaseEnum.MILVUS)
        knowledgeBase.setEbdModel(EmbeddingModelNameEnum.TEXT2VEC_BASE_CHINESE.getName())
        String fields = "[{\"fieldName\":\"clauseContent\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"分句后内容\",\"asEmbedding\":true},{\"fieldName\":\"docContent\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"语料数据内容\",\"asEmbedding\":false},{\"fieldName\":\"docId\",\"fieldType\":2,\"defaultValue\":\"0\",\"fieldDesc\":\"文档ID\",\"asEmbedding\":false},{\"fieldName\":\"docType\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"语料类型\",\"asEmbedding\":false},{\"fieldName\":\"poiId\",\"fieldType\":2,\"defaultValue\":\"\",\"fieldDesc\":\"门店ID\",\"asEmbedding\":false}]";
        knowledgeBase.setFields(GsonUtil.fromJson(fields, new TypeToken<List<KnowledgeBaseField>>() {
        }.getType()))
        String fieldMapping = "[{\"vecFieldName\":\"poiId\",\"knowledgeFieldNames\":[\"poiId\"]},{\"vecFieldName\":\"docId\",\"knowledgeFieldNames\":[\"docId\"]},{\"vecFieldName\":\"docType\",\"knowledgeFieldNames\":[\"docType\"]}]";
        knowledgeBase.setFieldMapping(GsonUtil.fromJson(fieldMapping, new TypeToken<List<KnowledgeBaseMappingField>>() {
        }.getType()))
        String storage_info = "{\"esStorageInfo\":{\"indexName\":\"daodian_hotel_dhimbot_corpus\"},\"milvusStorageInfo\":{\"tenant\":\"daodian_hotel\",\"cluster\":\"milvus_test01\",\"collection\":\"daodian_hotel_dhimbot_corpus_test\",\"accessKeyName\":\"daodian_hotel_prod_hotel_access_key\",\"accessAppKey\":\"com.sankuai.cms.knowledge.build\"}}"
        knowledgeBase.setStorageInfo(GsonUtil.fromJson(storage_info, KnowledgeStorageInfo.class))

        knowledgeBaseDO = new KnowledgeBaseDO()
        knowledgeBaseDO.setId(3L)
        knowledgeBaseDO.setName("国内SA酒店语料知识库")
        knowledgeBaseDO.setKnowledgeType(KnowledgeTypeEnum.TEXT.getValue())
        knowledgeBaseDO.setVecBase(VecBaseEnum.MILVUS.getValue())
        knowledgeBaseDO.setEbdModel(EmbeddingModelNameEnum.TEXT2VEC_BASE_CHINESE.getName())
        knowledgeBaseDO.setFields(fields)
        knowledgeBaseDO.setFieldMapping(fieldMapping)
        knowledgeBaseDO.setStorageInfo(storage_info)

        resultJson = "{\"knowledgeBaseId\":3,\"knowledgeBaseName\":\"国内SA酒店语料知识库\",\"knowledgeSourceName\":null,\"result\":[{\"fieldData\":[{\"fieldName\":\"clauseContent\",\"fieldValue\":\"[\\\"酒店地理位置很好，交通方便，房间舒适卫生，\\\",\\\"服务人员很热情，乐于提供各种帮助，\\\",\\\"小姑娘非常热情，前台小姐姐Anna（李小艳）很棒值得奖励。\\\"]\"},{\"fieldName\":\"docContent\",\"fieldValue\":\"酒店地理位置很好，\\\\n交通方便，房间舒适卫生，\\\\n服务人员很热情，乐于提供各种帮助，\\\\n小姑娘非常热情，前台小姐姐Anna（李小艳）很棒值得奖励。\"},{\"fieldName\":\"docId\",\"fieldValue\":\"1031791153206581734\"},{\"fieldName\":\"docType\",\"fieldValue\":\"userComment\"},{\"fieldName\":\"poiId\",\"fieldValue\":\"1633955096\"}],\"score\":0.8}]}"
    }

    @Unroll
    def "test queryByIds query"() {
        given:
        knowledgeBaseMapper.selectByIds(_) >> Lists.newArrayList(knowledgeBaseDO)
        when:
        List<KnowledgeBase> list = knowledgeBaseService.queryByIds(Lists.newArrayList(3L))
        KnowledgeBase resultData = list.get(0)
        then:
        list.size() == 1
        VecBaseEnum.MILVUS == resultData.getVecBase()
        resultData.getFields().size() == 5
        resultData.getFunction().getParamInfo().getParams().size() == 3
    }

}
