package com.sankuai.gaigc.arrange.common.core.bot.enhance

import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.BotEnhanceItem
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.QueryRecommendEnhance
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotEnhanceParallelResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult
import com.sankuai.gaigc.arrange.common.core.bot.enhance.start.QueryRecommendStartEnhanceHandler
import com.sankuai.gaigc.arrange.common.core.bot.enhance.success.QueryRecommendEnhanceHandler
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService
import com.sankuai.gaigc.arrange.common.core.bot.service.impl.SimpleKnowledgeRecallServiceImpl
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.FlowClient
import com.sankuai.gaigc.arrange.common.util.ApplicationContextUtil
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil
import com.sankuai.gaigc.arrange.config.MccConfig
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

import static org.powermock.api.mockito.PowerMockito.mockStatic
import static org.powermock.api.mockito.PowerMockito.when


@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest(ApplicationContextUtil.class)
@SuppressStaticInitializationFor(["com.javakk.spock.util.LogUtils"])
class QueryRecommendStartEnhanceHandlerSpec extends Specification {
    QueryRecommendStartEnhanceHandler queryRecommendStartEnhanceHandler

    def setup() {
        queryRecommendStartEnhanceHandler = new QueryRecommendStartEnhanceHandler()
    }

    @Unroll
    def "flowAccessIntent"() {
        given:
        String enhanceConfigJson = "{\"contentSource\":3,\"enableMultiIntent\":false,\"knowledgeBaseConfig\":{\"reuseBotKnowledge\":false,\"knowledgeBaseId\":8,\"score\":0.9,\"topK\":3,\"knowledgeFilterRule\":\"\",\"outputFieldName\":\"question\"},\"userConfig\":{\"limit\":3,\"content\":{\"ticket_info_inquiry\":[\"老人票怎么买？\",\"小孩票怎么买？\"],\"greeting_intent\":[\"12hjh21\",\"2121\"]}},\"flowAccessConfig\":{\"flowId\":\"1677\",\"limit\":\"3\"},\"id\":2,\"open\":true,\"className\":\"QueryRecommendEnhance\"}"
        BotEnhanceItem enhanceConfig = GsonUtil.fromJson(enhanceConfigJson, QueryRecommendEnhance.class)
        mockStatic(ApplicationContextUtil.class)
        FlowClient flowClient = Mock(FlowClient.class)
        when(ApplicationContextUtil.getBean(FlowClient.class)).thenReturn(flowClient)
        flowClient.execute(_,_) >> ["guessYouWant":["123","456"]]
        AIBot aiBot = AIBot.builder().id(478).build()
        String runParamJson = "{\"botId\":478,\"userMsg\":\"为什么消费者给的都是5分\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1},\"bizMsgHistory\":null,\"bizParams\":{\"identification\":\"FEXYOQ7T7P\",\"query\":\"为什么消费者给的都是5分\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1}},\"sessionId\":\"24fc54ff-4405-4f3f-a264-3770a3dbf1a0\",\"historyMessages\":[],\"compressMessages\":[],\"debug\":false,\"stream\":true}"
        BotRunParam runParam = GsonUtil.fromJson(runParamJson, BotRunParam.class)

        when:
        BotEnhanceParallelResult botEnhanceParallelResult =  queryRecommendStartEnhanceHandler.parallelHandle(enhanceConfig, runParam, aiBot)
        then:
        botEnhanceParallelResult.getResult().getQueryRecommend().contains("123")
        botEnhanceParallelResult.getResult().getQueryRecommend().contains("456")
        println botEnhanceParallelResult.getResult().getQueryRecommend()
    }
}