package com.sankuai.gaigc.arrange.common.core.thrift

import com.sankuai.gaigc.arrange.api.entity.bot.BotRunRequest
import com.sankuai.gaigc.arrange.api.entity.bot.BotRunResponse
import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.abtest.ArenaAbTestService
import com.sankuai.gaigc.arrange.common.core.bot.abtest.BotAbTestResponse
import com.sankuai.gaigc.arrange.common.core.bot.abtest.impl.ArenaAbTestServiceImpl
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.AnswerReferenceKnowledge
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotCardReplyBody
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotCardResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotEnhanceResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotSnapshotService
import com.sankuai.gaigc.arrange.common.core.monitor.AppExecutionRecorder
import com.sankuai.gaigc.arrange.common.core.thrift.impl.BotRunServiceTempImpl
import spock.lang.Specification
import spock.lang.Unroll

class BotRunServiceTempImplSpec extends Specification {
    AIBotSnapshotService botSnapshotService = Mock(AIBotSnapshotService)
    AppExecutionRecorder appExecutionRecorder = Mock(AppExecutionRecorder)
    BotRunServiceTempImpl service = new BotRunServiceTempImpl()
    ArenaAbTestService arenaAbTestService = new ArenaAbTestServiceImpl()

    def setup() {
        service.botSnapshotService = botSnapshotService
        service.appExecutionRecorder = appExecutionRecorder
        service.arenaAbTestService = arenaAbTestService
    }

    @Unroll
    def "测试 runBot 方法正常执行返回成功"() {
        given:
        BotRunRequest request = new BotRunRequest()
        request.setId(1L)
        AIBot bot = Mock(AIBot)

        Map<String, Object> testData = ["key1": "value1"]
        BotRunResult botRunResult = new BotRunResult(true, testData, "成功")
        botRunResult.setReplyContent("testReply")

        BotEnhanceResult enhanceResult = new BotEnhanceResult()
        enhanceResult.setExpandInfos(new ArrayList<Map<String, Object>>())
        enhanceResult.setQueryRecommend(["test1","test2"])
        AnswerReferenceKnowledge knowledge = new AnswerReferenceKnowledge();
        knowledge.setKnowledgeBaseId(1L);
        knowledge.setKnowledgeBaseName("示例知识库");
        knowledge.setKnowledgeContent("这是一条示例知识内容");
        knowledge.setReferenceScore(0.85);
        knowledge.setDocId(100L);

        enhanceResult.setReferenceKnowledge([knowledge])

        botRunResult.setEnhanceResult(enhanceResult)

        BotCardResult botCardResult1 = new BotCardResult()
        BotCardReplyBody botCardReplyBody = new BotCardReplyBody()
        botCardReplyBody.setReplyContent("1")
        botCardResult1.setReplyBody([botCardReplyBody])
        botCardResult1.setTraceMap(new HashMap<String, Object>())

        List<BotCardResult> botCardResult = [botCardResult1]
        botRunResult.setBotCardResult(botCardResult)

        BotAbTestResponse botAbTestResponse = new BotAbTestResponse(true,1)

        botSnapshotService.generateAIBot(_,_) >> bot
        bot.processQueryByPlan(_) >> botRunResult
        arenaAbTestService.executeBotAbTest(_) >> botAbTestResponse

        when:
        BotRunResponse response = service.runBot(request)

        then:
        response.code == 200
        response.data.replyContent == "testReply"
    }

}