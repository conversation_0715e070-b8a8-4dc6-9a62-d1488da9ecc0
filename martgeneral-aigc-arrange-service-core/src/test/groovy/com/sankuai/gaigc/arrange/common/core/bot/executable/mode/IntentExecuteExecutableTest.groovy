package com.sankuai.gaigc.arrange.common.core.bot.executable.mode

import co.elastic.clients.elasticsearch.inference.ModelConfig
import com.sankuai.gaigc.arrange.api.service.PromptFlowExecutionService
import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult
import com.sankuai.gaigc.arrange.common.core.bot.Function
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase
import com.sankuai.gaigc.arrange.common.core.bot.Plugin
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.BotExtensionConfig
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.BotModelConfig
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.FridayModelConfig
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.IntentRecognitionConfig
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutableIntentExecuteParam
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableExecuteSkill
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableLLM
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep
import com.sankuai.gaigc.arrange.common.core.bot.service.BotCardService
import com.sankuai.gaigc.arrange.common.core.promptflow.function.FunctionCallNotExecuteResult
import com.sankuai.gaigc.arrange.dao.dal.entity.AigcFlowSnapshot
import com.sankuai.gaigc.arrange.dao.dal.example.AigcFlowSnapshotExample
import com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFlowSnapshotMapper
import spock.lang.Specification
import spock.lang.Unroll
import com.meituan.mdp.langmodel.api.message.AssistantMessage
import com.meituan.mdp.langmodel.api.message.UserMessage
import com.meituan.mdp.langmodel.api.message.Message

class IntentExecuteExecutableTest extends Specification {

    IntentExecuteExecutable intentExecute = new IntentExecuteExecutable()
    AigcFlowSnapshotMapper flowSnapshotMapper = Mock()
    PromptFlowExecutionService promptFlowExecutionService = Mock()
    ExecutableExecuteSkill executableExecuteSkill = Mock()
    BotCardService botCardService = Mock()
    ExecutableLLM executableLLM = Mock()

    def setup() {
        intentExecute.flowSnapshotMapper = flowSnapshotMapper
        intentExecute.promptFlowExecutionService = promptFlowExecutionService
        intentExecute.executableExecuteSkill = executableExecuteSkill
        intentExecute.botCardService = botCardService
        intentExecute.executableLLM = executableLLM
    }

    @Unroll
    def "测试 execute 方法正常执行场景"() {
        given:
        ExecutePlanContext context = new ExecutePlanContext()
        context.bot = new AIBot()
        Plugin plugin = new Plugin()
        Function function = new Function()
        function.setName("testFunction")
        plugin.functionList = [function]

        context.bot.plugins = [plugin]

        Map<String, Object> intentionRecognitionResult = [answer: '{"name":"testFunction"}']
        ExecuteStep step = new ExecuteStep()
        AigcFlowSnapshotExample example = new AigcFlowSnapshotExample()
        example.createCriteria().andFlowIdEqualTo(flowId).andStatusEqualTo(1)
        AigcFlowSnapshot snapshot = new AigcFlowSnapshot()
        snapshot.setDetail('{"nodeInstances":[{"componentName":"InputComponent","inputParam":{"inputs":{"key":"value"}}}]}')
        flowSnapshotMapper.selectByExampleWithBLOBs(example) >> [snapshot]
        executableExecuteSkill.execute(_, _) >> new ExecuteResult(true, null, "Success")
        botCardService.functionNeedCardOutput(_, _) >> false

        when:
        ExecuteResult result = intentExecute.execute(context, intentionRecognitionResult, step)

        then:
        result.success == true
        result.message == "Success"

        where:
        flowId << [1L, 2L, 3L]
    }

    @Unroll
    def "测试 execute 方法notmatchfunction的情况"() {
        given:
        ExecutePlanContext context = new ExecutePlanContext()
        context.bot = new AIBot()
        Plugin plugin = new Plugin()
        Function function = new Function()
        function.setName("testFunction1")
        plugin.functionList = [function]

        context.bot.plugins = [plugin]
        context.bot.setModelConfig(new BotModelConfig());
        context.bot.getModelConfig().setRagModel(new FridayModelConfig());
        context.bot.getModelConfig().setAppId("testAppId");

        List<KnowledgeBase> knowledgeBaseList = new ArrayList<>()
        knowledgeBaseList.add(new KnowledgeBase())
        context.bot.setKnowledgeBases(knowledgeBaseList)

        Map<String, Object> intentionRecognitionResult = [answer: '{"name":"testFunction"}']
        ExecuteStep step = new ExecuteStep()

        ExecutableIntentExecuteParam param = new ExecutableIntentExecuteParam(new BotKnowledgeRecallResult())
        step.setCustomParam(param)

        executableLLM.execute(_,_) >> new ExecuteResult(true, null, "Success")

        when:
        ExecuteResult result = intentExecute.execute(context, intentionRecognitionResult, step)

        then:
        result.success == true
        result.message == "Success"

        where:
        flowId << [1L, 2L, 3L]
    }

    @Unroll
    def "测试 parseResult 方法当 intentionRecognitionResult 包含有效的 answer 时"() {
        given:
        Map<String, Object> intentionRecognitionResult = [answer: '{"name":"testIntent","arguments":{}}']
        AIBot bot = new AIBot()

        when:
        FunctionCallNotExecuteResult result = intentExecute.parseResult(intentionRecognitionResult, bot)

        then:
        result.name == "testIntent"
        result.arguments != null
    }

    @Unroll
    def "测试 parseResult 方法当 intentionRecognitionResult 的 answer 无效时"() {
        given:
        Map<String, Object> intentionRecognitionResult = [answer: '非 JSON 字符串']
        AIBot bot = AIBot.builder().extension(new BotExtensionConfig()).build()
        BotExtensionConfig botExtensionConfig = bot.getExtension()
        IntentRecognitionConfig intentRecognitionConfig = new IntentRecognitionConfig()
        intentRecognitionConfig.setDefaultIntent("other_inquiry")
        intentRecognitionConfig.setDefaultSecurityIntent("security_intent")
        botExtensionConfig.setIntentConfig(intentRecognitionConfig)

        when:
        FunctionCallNotExecuteResult result = intentExecute.parseResult(intentionRecognitionResult, bot)

        then:
        result.name == "other_inquiry"
        result.arguments.isEmpty()
    }


}
