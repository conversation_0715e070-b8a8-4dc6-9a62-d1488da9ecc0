package com.sankuai.gaigc.arrange


import com.sankuai.gaigc.arrange.api.entity.waihu.WaiHuTtsConfig
import com.sankuai.gaigc.arrange.common.constant.MessageTypeConstants
import com.sankuai.gaigc.arrange.common.core.bot.constants.AIBotRoleConstants
import com.sankuai.gaigc.arrange.common.core.bot.stream.WaihuWrapperSseEmitter
import com.sankuai.gaigc.arrange.common.model.SSEMessage
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import spock.lang.Specification

import static com.sankuai.gaigc.arrange.common.core.stream.FlowStreamController.STREAM_CHAT_TIMEOUT

class WaihuWrapperSseEmitterSpec extends Specification {


    def "test name"() {
        given:
        true
        when:
        //投放配置
        WaiHuTtsConfig WaiHuTtsConfig = new WaiHuTtsConfig()
        SseEmitter emitter = WaihuWrapperSseEmitter.wrap(STREAM_CHAT_TIMEOUT, "request.getTurnId()",
                WaiHuTtsConfig);

        emitter.send(null)
        SSEMessage sseMessage = SSEMessage.builder().content("asdasd").error("asdasd").role(AIBotRoleConstants.ASSISTANT).type(MessageTypeConstants.ANSWER)
                .index(-1)
                .build()
        emitter.send(sseMessage)
        then:
        emitter != null
    }

    def "test name2"() {
        given:
        true
        when:
        //投放配置
        WaiHuTtsConfig WaiHuTtsConfig = null
        SseEmitter emitter = WaihuWrapperSseEmitter.wrap(STREAM_CHAT_TIMEOUT, "request.getTurnId()",
                WaiHuTtsConfig);
        then:
        emitter != null
    }


    def "test name3"() {
        given:
        true
        when:
        //投放配置
        WaiHuTtsConfig WaiHuTtsConfig = new WaiHuTtsConfig()
        SseEmitter emitter = WaihuWrapperSseEmitter.wrap(STREAM_CHAT_TIMEOUT, "request.getTurnId()",
                WaiHuTtsConfig);

        emitter.send(null)
        SSEMessage sseMessage = SSEMessage.builder().content("asdasd").error("asdasd").role(AIBotRoleConstants.ASSISTANT).type(MessageTypeConstants.ANSWER)
                .index(1)
                .is_finish(false)
                .section_id("1").session_id("2")
                .build()
        emitter.send(sseMessage)
        then:
        emitter != null
    }

    def "test name4"() {
        given:
        true
        when:
        //投放配置
        WaiHuTtsConfig WaiHuTtsConfig = new WaiHuTtsConfig()
        SseEmitter emitter = WaihuWrapperSseEmitter.wrap(STREAM_CHAT_TIMEOUT, "request.getTurnId()",
                WaiHuTtsConfig);

        emitter.send(null)
        SSEMessage sseMessage = SSEMessage.builder().content("asdasd???").error("asdasd").role(AIBotRoleConstants.ASSISTANT).type(MessageTypeConstants.ANSWER)
                .index(1)
                .is_finish(false)
                .section_id("1").session_id("2")
                .build()
        emitter.send(sseMessage)
        then:
        emitter != null
    }

    def "test name5"() {
        given:
        true
        when:
        //投放配置
        WaiHuTtsConfig WaiHuTtsConfig = new WaiHuTtsConfig()
        SseEmitter emitter = WaihuWrapperSseEmitter.wrap(STREAM_CHAT_TIMEOUT, "request.getTurnId()",
                WaiHuTtsConfig);

        SSEMessage sseMessage = SSEMessage.builder().content("asdasd???").error("asdasd").role(AIBotRoleConstants.ASSISTANT).type(MessageTypeConstants.ANSWER)
                .index(1)
                .is_finish(false)
                .section_id("1").session_id("2")
                .build()

        def emitter1 = new WaihuWrapperSseEmitter(111L)
        emitter1.processFinalMessage(sseMessage)
        then:
        emitter != null
    }
}