package com.sankuai.gaigc.arrange.common.core.bot.enhance

import com.google.gson.reflect.TypeToken
import com.meituan.nibhtp.os.htp.cms.access.thrift.resp.serch.JsonSearchResp
import com.meituan.nibhtp.os.htp.cms.access.thrift.service.common.JsonContentThriftService
import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.BotEnhanceItem
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.ExpandInfoEnhance
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotEnhanceParallelResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult
import com.sankuai.gaigc.arrange.common.core.bot.enhance.start.ExpandInfoEnhanceHandler
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlan
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService
import com.sankuai.gaigc.arrange.common.core.bot.service.impl.SimpleKnowledgeRecallServiceImpl
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil
import com.sankuai.gaigc.arrange.config.MccConfig
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult
import org.apache.commons.collections.CollectionUtils
import org.junit.runner.RunWith
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
class ExpandInfoEnhanceHandlerSpec extends Specification {
    ExpandInfoEnhanceHandler expandInfoEnhanceHandler
    AIBotKnowledgeBaseService knowledgeBaseService
    SimpleKnowledgeRecallServiceImpl simpleKnowledgeRecallService
    MccConfig mccConfig
    JsonContentThriftService jsonContentThriftService;

    def setup() {
        knowledgeBaseService = Mock()
        simpleKnowledgeRecallService = Mock()
        mccConfig = Mock()
        jsonContentThriftService = Mock()
        expandInfoEnhanceHandler = new ExpandInfoEnhanceHandler(knowledgeBaseService: knowledgeBaseService, simpleKnowledgeRecallService: simpleKnowledgeRecallService, mccConfig: mccConfig, jsonContentThriftService: jsonContentThriftService)
    }

    @Unroll
    def "testConditionAndHighLightWords"() {
        given:
        String enhanceConfigJson = "{\"id\":2,\"open\":true,\"sort\":2,\"contentSource\":2,\"knowledgeBaseConfig\":{\"knowledgeBaseId\":9,\"score\":0.88,\"topK\":3,\"outputFieldNames\":[\"question\",\"answer\",\"docId\",\"sourceId\"]},\"conditions\":[{\"variable\":\"subIntent\",\"relation\":\"not in\",\"values\":[\"133979_get_hotel_site\",\"133976_get_hotel_contact\"]},{\"variable\":\"userInput\",\"relation\":\"not regex\",\"values\":[\"小时房|钟点房\",\"(怎么|咋)(办理)?入住\"]}],\"highlightWords\":\"消费者\"}"
        BotEnhanceItem enhanceConfig = GsonUtil.fromJson(enhanceConfigJson, ExpandInfoEnhance.class)
        String runParamJson = runParamJsonCase
        BotRunParam runParam = GsonUtil.fromJson(runParamJson, BotRunParam.class)
        AIBot aiBot = AIBot.builder().id(478).build()

        String knowledgeBasesJson = "[{\"id\":8,\"name\":\"住宿B端问答知识库\",\"description\":\"住宿B端问答知识库\",\"category\":\"STRUCTURED_KNOWLEDGE_BASE\",\"knowledgeType\":\"TEXT\",\"vecBase\":\"MILVUS\",\"embeddingModel\":2,\"fields\":[{\"fieldName\":\"docId\",\"fieldType\":2,\"defaultValue\":\"\",\"fieldDesc\":\"文档ID\",\"asEmbedding\":false},{\"fieldName\":\"ebdFieldName\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"向量化字段名称\",\"asEmbedding\":false},{\"fieldName\":\"sourceId\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"渠道ID\",\"asEmbedding\":false},{\"fieldName\":\"question\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"问题\",\"asEmbedding\":true},{\"fieldName\":\"answer\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"答案\",\"asEmbedding\":true},{\"fieldName\":\"appendInfo\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"附加信息\",\"asEmbedding\":false}],\"fieldMapping\":[{\"vecFieldName\":\"docId\",\"knowledgeFieldNames\":[\"docId\"],\"joinFormat\":null},{\"vecFieldName\":\"ebdFieldName\",\"knowledgeFieldNames\":[\"ebdFieldName\"],\"joinFormat\":null},{\"vecFieldName\":\"sourceId\",\"knowledgeFieldNames\":[\"sourceId\"],\"joinFormat\":null},{\"vecFieldName\":\"appendInfo\",\"knowledgeFieldNames\":[\"appendInfo\"],\"joinFormat\":null}],\"storageInfo\":{\"vexStorageInfo\":null,\"esStorageInfo\":{\"indexName\":\"daodian_hotel_bend_qa_knowledge\"},\"milvusStorageInfo\":{\"tenant\":\"daodian_hotel\",\"cluster\":\"milvus_test01\",\"collection\":\"daodian_hotel_bend_qa_knowledge_test2\",\"accessKeyName\":\"daodian_hotel_prod_hotel_access_key\",\"accessAppKey\":\"com.sankuai.cms.knowledge.build\"}},\"function\":{\"id\":8,\"name\":\"住宿B端问答知识库\",\"description\":\"住宿B端问答知识库\",\"funcType\":\"LOCAL_METHOD\",\"callInfo\":{\"serviceName\":\"com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.KnowledgeBaseRetrieve\",\"methodName\":\"execute\",\"isSpringBean\":true,\"timeout\":null},\"paramInfo\":{\"params\":[{\"identity\":\"docId\",\"name\":\"docId\",\"desc\":\"文档ID\",\"required\":false,\"dataType\":\"long\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"ebdFieldName\",\"name\":\"ebdFieldName\",\"desc\":\"向量化字段名称\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"sourceId\",\"name\":\"sourceId\",\"desc\":\"渠道ID\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"appendInfo\",\"name\":\"appendInfo\",\"desc\":\"附加信息\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null}],\"headers\":null,\"response\":null},\"flowId\":null,\"ext\":null}}]"
        List<KnowledgeBase> knowledgeBases = GsonUtil.fromJson(knowledgeBasesJson, new TypeToken<List<KnowledgeBase>>() {
        }.getType())
        knowledgeBaseService.queryByIds(_) >> knowledgeBases

        String retrieveResultJson = "{\"knowledgeBaseId\":8,\"knowledgeBaseName\":\"住宿B端问答知识库\",\"result\":[{\"fieldData\":{\"docId\":3053,\"sourceId\":\"64031962506\",\"question\":\"[\\\"为什么消费者给5星好评 评分是4.7呢\\\"]\",\"answer\":\"[\\\"评分具体计算逻辑对业务方是黑盒，评分逻辑复杂，不是简单的平均分，需要综合各类因素，即使商家的增加每一条评价都是5分好评，门店评分也不一定是5分，如商家有疑问请通过商服热线反馈：4006601065(工作时间：周一至周日9:00-23:00)，会有商服同事跟进处理。\\\",\\\"评分评价可参考：https://km.sankuai.com/page/713940927\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.9499483},{\"fieldData\":{\"docId\":33355,\"sourceId\":\"1919511236\",\"question\":\"[\\\"Q37：为什么消费者给的都是5分，门店的分数却不是5分？\\\"]\",\"answer\":\"[\\\"评分算法是通过多个影响因子计算每条评价的权重而得，不是算数平均分。\\\",\\\"因此即使商家的每一条评价都是5分，也不一定是5分。\\\",\\\"评分计算的主要考虑因素有：用户特征、评价内容质量、时间因素和评价诚信因素，一一对应的关系为内容质量越好的评价计算权重越高、评论时间越新的评价计算权重越高、评论人的用户专业度越高权重越高\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.9254234},{\"fieldData\":{\"docId\":33354,\"sourceId\":\"1919511236\",\"question\":\"[\\\"Q36：为什么已经有消费者给评价了，我门店的评分还是0分？\\\"]\",\"answer\":\"[\\\"点评侧会在第一条评论产生后的4天内开始计算评分;\\\",\\\" 美团侧第一天的评价，最快会在第二天，最晚会在第三天纳入评分的计算。;\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.9035551}]}"
        KnowledgeBaseRetrieveResult retrieveResult = GsonUtil.fromJson(retrieveResultJson, KnowledgeBaseRetrieveResult.class)
        simpleKnowledgeRecallService.retrieveFromKnowledgeBase(_, _, _, _, _) >> retrieveResult

        mccConfig.getDistinctThreshold() >> 0.8D
        when:
        BotEnhanceParallelResult botEnhanceParallelResult = expandInfoEnhanceHandler.parallelHandle(enhanceConfig, runParam, aiBot)
        then:
        CollectionUtils.isNotEmpty(botEnhanceParallelResult.getResult().getExpandInfos())
        GsonUtil.toJson(botEnhanceParallelResult.getResult().getExpandInfos().get(0).get("highLightWord")) == "[\"消费者\"]"
        where:
        runParamJsonCase | result
        "{\"botId\":478,\"userMsg\":\"为什么消费者给的都是5分\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1},\"bizMsgHistory\":null,\"bizParams\":{\"identification\":\"FEXYOQ7T7P\",\"query\":\"为什么消费者给的都是5分\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1},\"userInput\":\"酒店地址\",\"subIntent\":\"normalphone\"},\"sessionId\":\"24fc54ff-4405-4f3f-a264-3770a3dbf1a0\",\"historyMessages\":[],\"compressMessages\":[],\"debug\":false,\"stream\":true}" | null
    }

    @Unroll
    def "testGetReviewInfos"() {
        given:
        String enhanceConfigJson = "{\"id\":2,\"open\":true,\"sort\":2,\"contentSource\":2,\"knowledgeBaseConfig\":{\"knowledgeBaseId\":9,\"score\":0.88,\"topK\":3,\"outputFieldNames\":[\"answer\",\"reviewId\"]},\"conditions\":[{\"variable\":\"subIntent\",\"relation\":\"not in\",\"values\":[\"133979_get_hotel_site\",\"133976_get_hotel_contact\"]},{\"variable\":\"userInput\",\"relation\":\"not regex\",\"values\":[\"小时房|钟点房\",\"(怎么|咋)(办理)?入住\"]}],\"highlightWords\":\"消费者\"}"
        BotEnhanceItem enhanceConfig = GsonUtil.fromJson(enhanceConfigJson, ExpandInfoEnhance.class)
        String runResultJson = "{\"success\":true,\"message\":null,\"replyContent\":\"评分具体计算逻辑对业务方是黑盒，评分逻辑复杂，不是简单的平均分，需要综合各类因素，即使商家的增加每一条评价都是5分好评，门店评分也不一定是5分，如商家有疑问请通过商服热线反馈：4006601065(工作时间：周一至周日9:00-23:00)，会有商服同事跟进处理。评分评价可参考：https://km.sankuai.com/page/713940927。\",\"result\":{\"answer\":\"评分具体计算逻辑对业务方是黑盒，评分逻辑复杂，不是简单的平均分，需要综合各类因素，即使商家的增加每一条评价都是5分好评，门店评分也不一定是5分，如商家有疑问请通过商服热线反馈：4006601065(工作时间：周一至周日9:00-23:00)，会有商服同事跟进处理。评分评价可参考：https://km.sankuai.com/page/713940927。\"},\"processCode\":[],\"cards\":[],\"recall\":true,\"enhanceResult\":{},\"plan\":{\"name\":\"RAGCall\",\"steps\":[],\"executionSteps\":[{\"stepNo\":1,\"status\":\"SUCCESS\",\"steps\":[{\"step\":0,\"type\":\"knowledge\",\"objective\":null,\"executeTool\":null,\"sectionId\":\"*******************\",\"stepNo\":1,\"startTime\":1724314015818,\"executeResult\":{\"success\":true,\"objective\":null,\"result\":{},\"message\":\"\",\"recall\":true,\"processCodes\":[]},\"status\":\"SUCCESS\",\"skip\":false,\"hitCard\":false,\"customParam\":null,\"skipFunction\":null}]},{\"stepNo\":2,\"status\":\"SUCCESS\",\"steps\":[{\"step\":0,\"type\":\"answer\",\"objective\":null,\"executeTool\":\"testTool1\",\"sectionId\":\"2421488702817418647\",\"stepNo\":1,\"startTime\":1724314017560,\"executeResult\":{\"success\":true,\"objective\":null,\"result\":{\"answer\":\"评分具体计算逻辑对业务方是黑盒，评分逻辑复杂，不是简单的平均分，需要综合各类因素，即使商家的增加每一条评价都是5分好评，门店评分也不一定是5分，如商家有疑问请通过商服热线反馈：4006601065(工作时间：周一至周日9:00-23:00)，会有商服同事跟进处理。评分评价可参考：https://km.sankuai.com/page/713940927。\"},\"message\":\"\",\"recall\":false,\"processCodes\":null},\"status\":\"SUCCESS\",\"skip\":false,\"hitCard\":false,\"customParam\":{\"fillPromptFunction\":{},\"appId\":\"1693591208444199011\",\"fridayModelConfig\":{\"modelName\":\"gpt-4o-2024-05-13\",\"topP\":null,\"maxTokens\":800,\"temperature\":0.1,\"presencePenalty\":null,\"frequencyPenalty\":null},\"convertFunctionResult\":null},\"skipFunction\":null}]}],\"objective\":null,\"recall\":true}}"
        BotRunResult runResult = GsonUtil.fromJson(runResultJson, BotRunResult.class)
        String planJson = "{\"name\":\"RAGCall\",\"steps\":[],\"executionSteps\":[{\"stepNo\":1,\"status\":\"SUCCESS\",\"steps\":[{\"step\":0,\"type\":\"knowledge\",\"objective\":null,\"executeTool\":null,\"sectionId\":\"*******************\",\"stepNo\":1,\"startTime\":1724314015818,\"executeResult\":{\"success\":true,\"objective\":null,\"result\":{},\"message\":\"\",\"recall\":true,\"processCodes\":[]},\"status\":\"SUCCESS\",\"skip\":false,\"hitCard\":false,\"customParam\":null,\"skipFunction\":null}]},{\"stepNo\":2,\"status\":\"SUCCESS\",\"steps\":[{\"step\":0,\"type\":\"answer\",\"objective\":null,\"executeTool\":\"testTool1\",\"sectionId\":\"2421488702817418647\",\"stepNo\":1,\"startTime\":1724314017560,\"executeResult\":{\"success\":true,\"objective\":null,\"result\":{\"answer\":\"评分具体计算逻辑对业务方是黑盒，评分逻辑复杂，不是简单的平均分，需要综合各类因素，即使商家的增加每一条评价都是5分好评，门店评分也不一定是5分，如商家有疑问请通过商服热线反馈：4006601065(工作时间：周一至周日9:00-23:00)，会有商服同事跟进处理。评分评价可参考：https://km.sankuai.com/page/713940927。\"},\"message\":\"\",\"recall\":false,\"processCodes\":null},\"status\":\"SUCCESS\",\"skip\":false,\"hitCard\":false,\"customParam\":{\"fillPromptFunction\":{},\"appId\":\"1693591208444199011\",\"fridayModelConfig\":{\"modelName\":\"gpt-4o-2024-05-13\",\"topP\":null,\"maxTokens\":800,\"temperature\":0.1,\"presencePenalty\":null,\"frequencyPenalty\":null},\"convertFunctionResult\":null},\"skipFunction\":null}]}],\"objective\":null,\"recall\":true}"
        runResult.setPlan(GsonUtil.fromJson(planJson, ExecutePlan.class))
        String runParamJson = runParamJsonCase
        BotRunParam runParam = GsonUtil.fromJson(runParamJson, BotRunParam.class)
        AIBot aiBot = AIBot.builder().id(478).build()
        String knowledgeBasesJson = "[{\"id\":5,\"name\":\"住宿B端问答知识库\",\"description\":\"住宿B端问答知识库\",\"category\":\"STRUCTURED_KNOWLEDGE_BASE\",\"knowledgeType\":\"TEXT\",\"vecBase\":\"MILVUS\",\"embeddingModel\":2,\"fields\":[{\"fieldName\":\"docId\",\"fieldType\":2,\"defaultValue\":\"\",\"fieldDesc\":\"文档ID\",\"asEmbedding\":false},{\"fieldName\":\"ebdFieldName\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"向量化字段名称\",\"asEmbedding\":false},{\"fieldName\":\"sourceId\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"渠道ID\",\"asEmbedding\":false},{\"fieldName\":\"question\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"问题\",\"asEmbedding\":true},{\"fieldName\":\"answer\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"答案\",\"asEmbedding\":true},{\"fieldName\":\"appendInfo\",\"fieldType\":1,\"defaultValue\":\"\",\"fieldDesc\":\"附加信息\",\"asEmbedding\":false}],\"fieldMapping\":[{\"vecFieldName\":\"docId\",\"knowledgeFieldNames\":[\"docId\"],\"joinFormat\":null},{\"vecFieldName\":\"ebdFieldName\",\"knowledgeFieldNames\":[\"ebdFieldName\"],\"joinFormat\":null},{\"vecFieldName\":\"sourceId\",\"knowledgeFieldNames\":[\"sourceId\"],\"joinFormat\":null},{\"vecFieldName\":\"appendInfo\",\"knowledgeFieldNames\":[\"appendInfo\"],\"joinFormat\":null}],\"storageInfo\":{\"vexStorageInfo\":null,\"esStorageInfo\":{\"indexName\":\"daodian_hotel_bend_qa_knowledge\"},\"milvusStorageInfo\":{\"tenant\":\"daodian_hotel\",\"cluster\":\"milvus_test01\",\"collection\":\"daodian_hotel_bend_qa_knowledge_test2\",\"accessKeyName\":\"daodian_hotel_prod_hotel_access_key\",\"accessAppKey\":\"com.sankuai.cms.knowledge.build\"}},\"function\":{\"id\":8,\"name\":\"住宿B端问答知识库\",\"description\":\"住宿B端问答知识库\",\"funcType\":\"LOCAL_METHOD\",\"callInfo\":{\"serviceName\":\"com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.KnowledgeBaseRetrieve\",\"methodName\":\"execute\",\"isSpringBean\":true,\"timeout\":null},\"paramInfo\":{\"params\":[{\"identity\":\"docId\",\"name\":\"docId\",\"desc\":\"文档ID\",\"required\":false,\"dataType\":\"long\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"ebdFieldName\",\"name\":\"ebdFieldName\",\"desc\":\"向量化字段名称\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"sourceId\",\"name\":\"sourceId\",\"desc\":\"渠道ID\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"appendInfo\",\"name\":\"appendInfo\",\"desc\":\"附加信息\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null}],\"headers\":null,\"response\":null},\"flowId\":null,\"ext\":null}}]"
        List<KnowledgeBase> knowledgeBases = GsonUtil.fromJson(knowledgeBasesJson, new TypeToken<List<KnowledgeBase>>() {
        }.getType())
        knowledgeBaseService.queryByIds(_) >> knowledgeBases
        String retrieveResultJson = "{\"knowledgeBaseId\":5,\"knowledgeBaseName\":\"住宿B端问答知识库\",\"result\":[{\"fieldData\":{\"docId\":3053,\"reviewId\":\"64031962506\",\"question\":\"[\\\"为什么消费者给5星好评 评分是4.7呢\\\"]\",\"answer\":\"[\\\"评分具体计算逻辑对业务方是黑盒，评分逻辑复杂，不是简单的平均分，需要综合各类因素，即使商家的增加每一条评价都是5分好评，门店评分也不一定是5分，如商家有疑问请通过商服热线反馈：4006601065(工作时间：周一至周日9:00-23:00)，会有商服同事跟进处理。\\\",\\\"评分评价可参考：https://km.sankuai.com/page/713940927\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.9499483},{\"fieldData\":{\"docId\":33355,\"reviewId\":\"1919511236\",\"question\":\"[\\\"Q37：为什么消费者给的都是5分，门店的分数却不是5分？\\\"]\",\"answer\":\"[\\\"评分算法是通过多个影响因子计算每条评价的权重而得，不是算数平均分。\\\",\\\"因此即使商家的每一条评价都是5分，也不一定是5分。\\\",\\\"评分计算的主要考虑因素有：用户特征、评价内容质量、时间因素和评价诚信因素，一一对应的关系为内容质量越好的评价计算权重越高、评论时间越新的评价计算权重越高、评论人的用户专业度越高权重越高\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.9254234},{\"fieldData\":{\"docId\":33354,\"reviewId\":\"1919511237\",\"question\":\"[\\\"Q36：为什么已经有消费者给评价了，我门店的评分还是0分？\\\"]\",\"answer\":\"[\\\"点评侧会在第一条评论产生后的4天内开始计算评分;\\\",\\\" 美团侧第一天的评价，最快会在第二天，最晚会在第三天纳入评分的计算。;\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.9035551}]}"
        KnowledgeBaseRetrieveResult retrieveResult = GsonUtil.fromJson(retrieveResultJson, KnowledgeBaseRetrieveResult.class)
        simpleKnowledgeRecallService.retrieveFromKnowledgeBase(_, _, _, _, _) >> retrieveResult
        String jsonResp = "{\"items\":[{\"jsonData\":\"{\\\"originReviewId\\\":64031962506,\\\"addTime\\\":1730304701,\\\"source\\\":2,\\\"reviewId\\\":100001}\"},{\"jsonData\":\"{\\\"originReviewId\\\":1919511236,\\\"addTime\\\":1730304702,\\\"source\\\":2,\\\"reviewId\\\":100002}\"},{\"jsonData\":\"{\\\"originReviewId\\\":1919511237,\\\"addTime\\\":1730304703,\\\"source\\\":2,\\\"reviewId\\\":100003}\"}]}"
        jsonContentThriftService.search(_) >> GsonUtil.fromJson(jsonResp, JsonSearchResp.class)


        mccConfig.getDistinctThreshold() >> 0.8D
        when:
        BotEnhanceParallelResult botEnhanceParallelResult = expandInfoEnhanceHandler.parallelHandle(enhanceConfig, runParam, aiBot)
        then:
        CollectionUtils.isNotEmpty(botEnhanceParallelResult.getResult().getExpandInfos())
        botEnhanceParallelResult.getResult().getExpandInfos().get(0).get("reviewId") == 100001
        where:
        runParamJsonCase | result
        "{\"botId\":478,\"userMsg\":\"为什么消费者给的都是5分\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1},\"bizMsgHistory\":null,\"bizParams\":{\"poiId\":\"123\",\"identification\":\"FEXYOQ7T7P\",\"query\":\"为什么消费者给的都是5分\",\"user\":{\"userId\":null,\"userName\":\"chenyang131\",\"userType\":1},\"userInput\":\"酒店地址\",\"subIntent\":\"normalphone\"},\"sessionId\":\"24fc54ff-4405-4f3f-a264-3770a3dbf1a0\",\"historyMessages\":[],\"compressMessages\":[],\"debug\":false,\"stream\":true}" | null
    }

}