package com.sankuai.gaigc.arrange.common.core.bot.plan

import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam
import spock.lang.Specification
import spock.lang.Unroll
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult

class ExecutionStepTest extends Specification {
    @Unroll
    def "测试单步执行成功"() {
        given:
        ExecutionStep executionStep = new ExecutionStep()
        ExecuteStep mockStep = Mock(ExecuteStep)
        executionStep.setSteps([mockStep])
        ExecutePlanContext mockContext = Mock(ExecutePlanContext)
        ExecuteResult mockResult = new ExecuteResult(true, null, "成功")
        mockStep.execute(_) >> mockResult

        when:
        List<ExecuteResult> results = executionStep.singleStepExecute(mockContext)

        then:
        results.size() == 1
        results[0].success
        results[0].message == "成功"
    }

    @Unroll
    def "正常执行 parallelExecute 方法"() {
        given: "给定一个执行步骤和执行上下文"
        ExecutionStep executionStep = new ExecutionStep()
        ExecutePlanContext planContext = Mock(ExecutePlanContext)
        planContext.getRunParam() >> new BotRunParam()
        ExecuteStep step = Mock(ExecuteStep);
        step.execute(planContext) >>  new ExecuteResult(true, null, "Success")
        executionStep.setSteps([step])

        when: "调用 parallelExecute 方法"
        List<ExecuteResult> results = executionStep.parallelExecute(planContext)

        then: "所有步骤正常执行，返回成功的执行结果"
        assert results.size() == 1
    }
}
