package com.sankuai.gaigc.arrange.common.core.audio

import com.sankuai.gaigc.arrange.api.entity.audio.AudioVO
import com.sankuai.gaigc.arrange.api.entity.audio.TTSRequest
import com.sankuai.gaigc.arrange.api.entity.audio.TTSResult
import com.sankuai.gaigc.arrange.api.entity.common.Response
import com.sankuai.gaigc.arrange.api.enums.AudioSourceEnum
import com.sankuai.gaigc.arrange.api.enums.AudioStatusEnum
import com.sankuai.gaigc.arrange.common.core.audio.entity.AudioEntity
import com.sankuai.gaigc.arrange.common.core.audio.service.AudioService
import com.sankuai.gaigc.arrange.common.core.pigeon.impl.AudioRpcServiceImpl
import spock.lang.Specification

class AigcAudioEntitySpec extends Specification {




    def "text2audio测试"() {
        given:
        AudioService audioService = Mock()
        List<AudioEntity> onlineList = new ArrayList<>();
        AudioEntity entity = AudioEntity.create(40001, AudioSourceEnum.XM,"子玲（女）", "",
                "","","ziling_stream", null, AudioStatusEnum.ON)
        onlineList.add(entity)
        audioService.getOnLineAudioList() >> onlineList

        AudioRpcServiceImpl audioRpcService = new AudioRpcServiceImpl(audioService: audioService);

        AudioEntity audioEntity = AudioEntity.create(40001, AudioSourceEnum.XM,"子玲（女）", "",
                "","","ziling_stream", null, AudioStatusEnum.ON)



        when:
        Response<List<AudioVO>> response = audioRpcService.getOnLineAudioList()
        then:
        response.getData().size() == 1
    }

    def "text2Audio测试"() {
        given:
        AudioService audioService = Mock()
        TTSResult ttsResult = new TTSResult()
        audioService.text2Audio(_) >> ttsResult
        AudioRpcServiceImpl audioRpcService = new AudioRpcServiceImpl(audioService: audioService);

        when:
        TTSRequest ttsRequest = new TTSRequest()
        Response<TTSResult> resp = audioRpcService.text2Audio(ttsRequest)
        then:
        resp.isSuccess()
    }


    def 构造方法() {
        given:
        def o = new AudioVO()
        when:
        o.setAudioId(1)
        o.setName("test")
        o.setCoverImgUrl("test")
        o.setDemoUrl("test")
        o.setSource(1)
        o.setDesc("111")
        o.toString()
        o.setVoice("asda")
        def voice = o.getVoice()

        then:
        o != null
    }
}
