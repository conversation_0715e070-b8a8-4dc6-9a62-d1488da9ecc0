package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod

import com.meituan.hbdata.travel.ranking.thrift.rank.TravelRankService
import com.meituan.hbdata.travel.ranking.thrift.rank.TravelResponse
import com.meituan.hbdata.travel.ranking.thrift.rank.common.RankedItem
import com.meituan.hbdata.travel.ranking.thrift.rank.common.ResponseStatus
import com.meituan.hotel.data.ranking.message2.HotelResponse
import com.meituan.hotel.data.ranking.message2.PoiItem
import com.meituan.hotel.data.ranking.message2.RPCHotelRankService
import spock.lang.Specification
import spock.lang.Unroll

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @created: 2024/7/12
 * @description:
 */
class TravelNearbyPoiRecommendTest extends Specification {
    TravelNearbyPoiRecommend travelNearbyPoiRecommend
    TravelRankService.Iface travelRankService

    def setup() {

        travelRankService = Mock(TravelRankService.Iface)
        travelNearbyPoiRecommend = new TravelNearbyPoiRecommend(travelRankService:travelRankService)
    }


    /**
     * 测试正常流程
     */
    @Unroll
    def "test valid input"() {
        given:
        TravelResponse travelResponse = new TravelResponse()
        List<RankedItem> rankedItems = new ArrayList<>()
        rankedItems.add(new RankedItem(id: 111))
        rankedItems.add(new RankedItem(id: 222))
        travelResponse.setRankedItemList(rankedItems)
        travelResponse.setStatus(ResponseStatus.OK)
        travelRankService.searchRank(_) >> travelResponse
        when:
        List<Integer> recommendInfo = travelNearbyPoiRecommend.queryTravelNearbyPoiRecommend(0.0, 0.0, 1, "!")

        then:
        recommendInfo != null
    }

    /**
     * 测试正常流程
     */
    @Unroll
    def "test valid input with order"() {
        given:
        TravelResponse travelResponse = new TravelResponse()
        List<RankedItem> rankedItems = new ArrayList<>()
        rankedItems.add(new RankedItem(id: 111))
        rankedItems.add(new RankedItem(id: 222))
        travelResponse.setRankedItemList(rankedItems)
        travelResponse.setStatus(ResponseStatus.OK)
        travelRankService.searchRank(_) >> travelResponse
        when:
        List<Integer> recommendInfo = travelNearbyPoiRecommend.queryTravelNearbyPoiRecommend(0.0, 0.0, 1, "!", 1, "123" )

        then:
        recommendInfo != null
    }
}