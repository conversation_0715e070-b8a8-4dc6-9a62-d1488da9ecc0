package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod

import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotExecuteService
import com.sankuai.gaigc.arrange.config.MccConfig
import spock.lang.Specification
import spock.lang.Unroll


class DomesticHotelBotServiceTest extends Specification {
    DomesticHotelBotService domesticHotelBotService
    AIBotExecuteService botExecuteService
    MccConfig mccConfig

    def setup() {
        // 初始化 Mock 对象
        botExecuteService = Mock(AIBotExecuteService)
        mccConfig = Mock(MccConfig)
        domesticHotelBotService = new DomesticHotelBotService()
        domesticHotelBotService.botExecuteService = botExecuteService
        domesticHotelBotService.mccConfig = mccConfig
    }

    /**
     * 测试正常情况下的返回
     */
    @Unroll
    def "test runDomesticHotelBot with valid parameters"() {
        given:
        String appParamString = "appParam"
        String poiName = "poiName"
        List<Map<String, Object>> historyMessage = []
        String goodsParamString = "goodsParam"
        String poiId = "poiId"
        String userInput = "userInput"
        String userParamString = "userParam"
        String userId = "123"
        String token = "token"
        Map<String, Object> expectedResponse = [key: "value"]
        Long botId = 1L
        mccConfig.getDemesticHotelBotId() >> botId
        botExecuteService.runBot(_) >> expectedResponse

        when:
        Map<String, Object> result = domesticHotelBotService.runDomesticHotelBot(appParamString, poiName, historyMessage, goodsParamString, poiId, userInput, userParamString, userId, token)

        then:
        result == expectedResponse
    }

    @Unroll
    def "测试正常情况下的runDomesticHotelBotV2方法"() {
        given: "模拟输入参数和预期结果"
        Map<String, Object> bizParamsInput = [userId: "123", userInput: "Hello",userParamString:"{\"userId\":120734192,\"uuid\":\"0000000000000B5EC1D8EAEDD474E866D8D2249482C14A173263966690123307\",\"userCityId\":458,\"gpsCityId\":458,\"customGpsStatus\":1,\"lat\":28.930557677855884,\"lng\":120.05433649142894}"]
        Map<String, Object> expected = [result: "success"]
        mccConfig.getDemesticHotelBotId() >> 1L
        botExecuteService.runBot(_) >> expected

        when: "调用方法"
        Map<String, Object> result = domesticHotelBotService.runDomesticHotelBotV2(bizParamsInput)

        then: "验证结果"
        result == expected
    }


    @Unroll
    def "测试正常情况下的runDomesticHotelBotV3方法"() {
        given: "模拟输入参数和预期结果"
        Map<String, Object> bizParamsInput = [userId: "123", userInput: "Hello",userParamString:"{\"userId\":120734192,\"uuid\":\"0000000000000B5EC1D8EAEDD474E866D8D2249482C14A173263966690123307\",\"userCityId\":458,\"gpsCityId\":458,\"customGpsStatus\":1,\"lat\":28.930557677855884,\"lng\":120.05433649142894}"]
        Map<String, Object> expected = [result: "success"]
        mccConfig.getDemesticHotelBotId() >> 1L
        botExecuteService.runBot(_) >> expected

        when: "调用方法"
        Map<String, Object> result = domesticHotelBotService.runDomesticHotelBot(bizParamsInput)

        then: "验证结果"
        result == expected
    }
}