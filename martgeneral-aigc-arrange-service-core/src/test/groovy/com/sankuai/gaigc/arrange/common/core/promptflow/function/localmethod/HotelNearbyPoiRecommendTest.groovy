package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod


import com.meituan.hotel.data.ranking.message2.HotelResponse
import com.meituan.hotel.data.ranking.message2.PoiItem
import com.meituan.hotel.data.ranking.message2.RPCHotelRankService
import com.sankuai.gaigc.arrange.common.core.promptflow.function.enums.RoomAttributeEnum
import com.sankuai.gaigc.arrange.common.core.promptflow.function.enums.TdcRoomTypeEnum
import com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod.entity.HotelRoomAllResult
import spock.lang.Specification
import spock.lang.Unroll
/**
 * @author: zhang<PERSON>ahu<PERSON>
 * @created: 2024/7/12
 * @description:
 */
class HotelNearbyPoiRecommendTest extends Specification {
    HotelNearbyPoiRecommend hotelNearbyPoiRecommend
    RPCHotelRankService.Iface rpcHotelRankService

    def setup() {

        rpcHotelRankService = Mock(RPCHotelRankService.Iface)
        hotelNearbyPoiRecommend = new HotelNearbyPoiRecommend(rpcHotelRankService:rpcHotelRankService)
    }


    /**
     * 测试正常流程
     */
    @Unroll
    def "test valid input"() {
        given:
        HotelRoomAllResult hotelRoomAllResult = new HotelRoomAllResult()
        hotelRoomAllResult.setRoomId("")
        hotelRoomAllResult.getRoomId()
        hotelRoomAllResult.setRoomName("")
        hotelRoomAllResult.getRoomName()
        hotelRoomAllResult.setRoomType("")
        hotelRoomAllResult.getRoomType()
        hotelRoomAllResult.setCapacity("")
        hotelRoomAllResult.getCapacity()
        hotelRoomAllResult.setWindow("")
        hotelRoomAllResult.getWindow()
        hotelRoomAllResult.setFloor("")
        hotelRoomAllResult.getFloor()
        hotelRoomAllResult.setUseableArea("")
        hotelRoomAllResult.getUseableArea()
        hotelRoomAllResult.setAirConditioner("")
        hotelRoomAllResult.getAirConditioner()
        hotelRoomAllResult.setSeparateToilet("")
        hotelRoomAllResult.getSeparateToilet()
        hotelRoomAllResult.setGoodsStatus("")
        hotelRoomAllResult.getGoodsStatus()
        hotelRoomAllResult.setPrice(0.0)
        hotelRoomAllResult.getPrice()
        hotelRoomAllResult.setExtraBed("")
        hotelRoomAllResult.getExtraBed()
        hotelRoomAllResult.setWindowView("")
        hotelRoomAllResult.getWindowView()
        hotelRoomAllResult.setLastModifyTime("")
        hotelRoomAllResult.getLastModifyTime()
        hotelRoomAllResult.setTub("")
        hotelRoomAllResult.getTub()
        hotelRoomAllResult.setHairDryer("")
        hotelRoomAllResult.getHairDryer()
        hotelRoomAllResult.setMicrowaveOven("")
        hotelRoomAllResult.getMicrowaveOven()
        hotelRoomAllResult.setHeating("")
        hotelRoomAllResult.getHeating()
        hotelRoomAllResult.setShower("")
        hotelRoomAllResult.getShower()
        hotelRoomAllResult.setSmallRefrigerator("")
        hotelRoomAllResult.getSmallRefrigerator()
        hotelRoomAllResult.setSafa("")
        hotelRoomAllResult.getSafa()
        hotelRoomAllResult.setTv("")
        hotelRoomAllResult.getTv()
        hotelRoomAllResult.setWashingMachine("")
        hotelRoomAllResult.getWashingMachine()
        hotelRoomAllResult.setCurtain("")
        hotelRoomAllResult.getCurtain()
        hotelRoomAllResult.setKitchen("")
        hotelRoomAllResult.getKitchen()
        hotelRoomAllResult.setBalcony("")
        hotelRoomAllResult.getBalcony()
        hotelRoomAllResult.setSmoke("")
        hotelRoomAllResult.getSmoke()
        hotelRoomAllResult.setGamingComputer("")
        hotelRoomAllResult.getGamingComputer()
        hotelRoomAllResult.setComputerNum("")
        hotelRoomAllResult.getComputerNum()
        hotelRoomAllResult.setGraphicsChip("")
        hotelRoomAllResult.getGraphicsChip()
        hotelRoomAllResult.setCpuModel("")
        hotelRoomAllResult.getCpuModel()
        hotelRoomAllResult.setRunningMemory("")
        hotelRoomAllResult.getRunningMemory()
        hotelRoomAllResult.setNetWork("")
        hotelRoomAllResult.getNetWork()
        RoomAttributeEnum.AIR_CONDITIONER
        RoomAttributeEnum.AIR_CONDITIONER
        RoomAttributeEnum.SEPARATE_TOILET
        RoomAttributeEnum.HAIR_DRYER
        RoomAttributeEnum.MICROWAVE_OVEN
        RoomAttributeEnum.TUB
        RoomAttributeEnum.HEATING
        RoomAttributeEnum.SHOWER
        RoomAttributeEnum.SMALL_REFRIGERATOR
        RoomAttributeEnum.SOFA
        RoomAttributeEnum.TV
        RoomAttributeEnum.WASHING_MACHINE
        RoomAttributeEnum.CURTAIN
        RoomAttributeEnum.KITCHEN
        RoomAttributeEnum.BALCONY
        RoomAttributeEnum.SMOKE
        RoomAttributeEnum.GAMING_COMPUTER
        RoomAttributeEnum.COMPUTER_NUM
        RoomAttributeEnum.GRAPHICS_CHIP
        RoomAttributeEnum.CPU_MODEL
        RoomAttributeEnum.RUNNING_MEMORY
        RoomAttributeEnum.NETWORK
        TdcRoomTypeEnum.BIG_BED
        TdcRoomTypeEnum.SINGLE
        TdcRoomTypeEnum.DOUBLE_BED
        TdcRoomTypeEnum.TRIPLE
        TdcRoomTypeEnum.SUITE
        TdcRoomTypeEnum.DETACHED
        TdcRoomTypeEnum.BED_IN_ROOM


        HotelResponse hotelResponse = new HotelResponse()
        List<PoiItem> poiItems = new ArrayList<>()
        poiItems.add(new PoiItem(poiId: 111))
        poiItems.add(new PoiItem(poiId: 222))
        hotelResponse.setPoiResults(poiItems)
        rpcHotelRankService.search(_) >> hotelResponse
        when:
        List<Integer> recommendInfo = hotelNearbyPoiRecommend.queryHotelNearbyPoiRecommend(0.0, 0.0, 1, "!")

        then:
        recommendInfo != null
        recommendInfo.size() == 2
        recommendInfo == [111, 222]
    }


    /**
     * 测试正常流程
     */
    @Unroll
    def "test valid input v2"() {
        given:
        HotelRoomAllResult hotelRoomAllResult = new HotelRoomAllResult()
        hotelRoomAllResult.setRoomId("")
        hotelRoomAllResult.getRoomId()
        hotelRoomAllResult.setRoomName("")
        hotelRoomAllResult.getRoomName()
        hotelRoomAllResult.setRoomType("")
        hotelRoomAllResult.getRoomType()
        hotelRoomAllResult.setCapacity("")
        hotelRoomAllResult.getCapacity()
        hotelRoomAllResult.setWindow("")
        hotelRoomAllResult.getWindow()
        hotelRoomAllResult.setFloor("")
        hotelRoomAllResult.getFloor()
        hotelRoomAllResult.setUseableArea("")
        hotelRoomAllResult.getUseableArea()
        hotelRoomAllResult.setAirConditioner("")
        hotelRoomAllResult.getAirConditioner()
        hotelRoomAllResult.setSeparateToilet("")
        hotelRoomAllResult.getSeparateToilet()
        hotelRoomAllResult.setGoodsStatus("")
        hotelRoomAllResult.getGoodsStatus()
        hotelRoomAllResult.setPrice(0.0)
        hotelRoomAllResult.getPrice()
        hotelRoomAllResult.setExtraBed("")
        hotelRoomAllResult.getExtraBed()
        hotelRoomAllResult.setWindowView("")
        hotelRoomAllResult.getWindowView()
        hotelRoomAllResult.setLastModifyTime("")
        hotelRoomAllResult.getLastModifyTime()
        hotelRoomAllResult.setTub("")
        hotelRoomAllResult.getTub()
        hotelRoomAllResult.setHairDryer("")
        hotelRoomAllResult.getHairDryer()
        hotelRoomAllResult.setMicrowaveOven("")
        hotelRoomAllResult.getMicrowaveOven()
        hotelRoomAllResult.setHeating("")
        hotelRoomAllResult.getHeating()
        hotelRoomAllResult.setShower("")
        hotelRoomAllResult.getShower()
        hotelRoomAllResult.setSmallRefrigerator("")
        hotelRoomAllResult.getSmallRefrigerator()
        hotelRoomAllResult.setSafa("")
        hotelRoomAllResult.getSafa()
        hotelRoomAllResult.setTv("")
        hotelRoomAllResult.getTv()
        hotelRoomAllResult.setWashingMachine("")
        hotelRoomAllResult.getWashingMachine()
        hotelRoomAllResult.setCurtain("")
        hotelRoomAllResult.getCurtain()
        hotelRoomAllResult.setKitchen("")
        hotelRoomAllResult.getKitchen()
        hotelRoomAllResult.setBalcony("")
        hotelRoomAllResult.getBalcony()
        hotelRoomAllResult.setSmoke("")
        hotelRoomAllResult.getSmoke()
        hotelRoomAllResult.setGamingComputer("")
        hotelRoomAllResult.getGamingComputer()
        hotelRoomAllResult.setComputerNum("")
        hotelRoomAllResult.getComputerNum()
        hotelRoomAllResult.setGraphicsChip("")
        hotelRoomAllResult.getGraphicsChip()
        hotelRoomAllResult.setCpuModel("")
        hotelRoomAllResult.getCpuModel()
        hotelRoomAllResult.setRunningMemory("")
        hotelRoomAllResult.getRunningMemory()
        hotelRoomAllResult.setNetWork("")
        hotelRoomAllResult.getNetWork()
        RoomAttributeEnum.AIR_CONDITIONER
        RoomAttributeEnum.AIR_CONDITIONER
        RoomAttributeEnum.SEPARATE_TOILET
        RoomAttributeEnum.HAIR_DRYER
        RoomAttributeEnum.MICROWAVE_OVEN
        RoomAttributeEnum.TUB
        RoomAttributeEnum.HEATING
        RoomAttributeEnum.SHOWER
        RoomAttributeEnum.SMALL_REFRIGERATOR
        RoomAttributeEnum.SOFA
        RoomAttributeEnum.TV
        RoomAttributeEnum.WASHING_MACHINE
        RoomAttributeEnum.CURTAIN
        RoomAttributeEnum.KITCHEN
        RoomAttributeEnum.BALCONY
        RoomAttributeEnum.SMOKE
        RoomAttributeEnum.GAMING_COMPUTER
        RoomAttributeEnum.COMPUTER_NUM
        RoomAttributeEnum.GRAPHICS_CHIP
        RoomAttributeEnum.CPU_MODEL
        RoomAttributeEnum.RUNNING_MEMORY
        RoomAttributeEnum.NETWORK
        TdcRoomTypeEnum.BIG_BED
        TdcRoomTypeEnum.SINGLE
        TdcRoomTypeEnum.DOUBLE_BED
        TdcRoomTypeEnum.TRIPLE
        TdcRoomTypeEnum.SUITE
        TdcRoomTypeEnum.DETACHED
        TdcRoomTypeEnum.BED_IN_ROOM


        HotelResponse hotelResponse = new HotelResponse()
        List<PoiItem> poiItems = new ArrayList<>()
        poiItems.add(new PoiItem(poiId: 111))
        poiItems.add(new PoiItem(poiId: 222))
        hotelResponse.setPoiResults(poiItems)
        rpcHotelRankService.search(_) >> hotelResponse
        when:
        List<Integer> recommendInfo = hotelNearbyPoiRecommend.queryHotelNearbyPoiRecommend(0.0, 0.0, 1, "!", "userId", 1 )

        then:
        recommendInfo != null
        recommendInfo.size() == 2
        recommendInfo == [111, 222]
    }
}