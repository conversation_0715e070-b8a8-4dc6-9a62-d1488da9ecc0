package com.sankuai.gaigc.arrange.promptflow.component;


import com.google.common.collect.Lists;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;


@PromptFlowComponent(name = "mergeTextAndMisIdComp", desc="Operator1", type = ComponentTypeEnum.NONE, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition(
        {
                @Param(name = "text", required = true, type = ParamTypeEnum.STRING),
                @Param(name = "misId", required = true, type = ParamTypeEnum.STRING),
        }
)
@Slf4j
@OutputParamDefinition(
        {
                @Param(name = "outputAt", required = true, type = ParamTypeEnum.STRING)
        }
)
public class MergeTextAndMisIdComp extends AbstractComponent {
    private static final String KEY_NAME_TEXT = "text";

    private static final String KEY_NAME_MISID = "misId";

    private static final String KEY_NAME_OUTPUT_AT = "outputAt";

    @Override
    public Map<String, Object> execute (Context context, ComponentInfo componentInfo) throws Exception {
        Object textValue = parseParam(KEY_NAME_TEXT, componentInfo);
        Object mergedValue = parseParam(KEY_NAME_MISID, componentInfo);
        String result = textValue + "#mergeTextAndMisIdComp#" + mergedValue;
        putMidRes2Context(KEY_NAME_OUTPUT_AT, result, componentInfo);
        return output(Lists.newArrayList(KEY_NAME_OUTPUT_AT), Lists.newArrayList(result));
    }
}