package com.sankuai.gaigc.arrange.example.simpledemo;



import com.sankuai.gaigc.arrange.common.core.taskflow.operator.IOperator;

import java.util.concurrent.TimeUnit;

public class Operator1 implements IOperator<Integer, Integer> {
    @Override
    public Integer execute(Integer param) throws Exception {
        //业务逻辑部分
        TimeUnit.SECONDS.sleep(1);
        System.out.println("Operator1...");
        return null;
    }
}