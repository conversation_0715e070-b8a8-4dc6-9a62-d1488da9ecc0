package com.sankuai.gaigc.arrange.promptflow.component.comp;

import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.GeneralPrompt;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentParam;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wenhao10
 * @Description:
 * @Date: 2023-08-17 19:00
 */
public class GeneralPromptTest {
    @Test
    public void test() throws Exception {
        GeneralPrompt generalPrompt = new GeneralPrompt();
        ComponentInfo componentInfo = new ComponentInfo();
        Map<String, ComponentParam> params = CompParamsUtil.buildInput(new HashMap<String, Object[]>() {{
            put("promptId", new Object[]{"2", ParamTypeEnum.STRING});
            put("content", new Object[]{"this is a {{weather}} day, so get off {{adv}}", ParamTypeEnum.STRING});
            Map<String, Object> promptParamMap = new HashMap<>();
            promptParamMap.put("weather", "rainy");
            promptParamMap.put("adv", "early");
            put("paramsMap", new Object[]{promptParamMap, ParamTypeEnum.MAP});
        }});
        componentInfo.setInputs(params);
        Map<String, Object> execute = generalPrompt.execute(null, componentInfo);
        Assert.assertEquals("this is a rainy day, so get off early", execute.get("prompt"));
    }
}
