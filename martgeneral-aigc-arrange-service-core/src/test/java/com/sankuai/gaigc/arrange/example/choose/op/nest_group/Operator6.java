package com.sankuai.gaigc.arrange.example.choose.op.nest_group;


import com.sankuai.gaigc.arrange.common.core.taskflow.operator.IOperator;

import java.util.concurrent.TimeUnit;

public class Operator6 implements IOperator<Integer, Integer> {
    @Override
    public Integer execute(Integer param) throws Exception {
        //业务逻辑部分
        TimeUnit.MILLISECONDS.sleep(1000);
        System.out.println("6...");
        return null;
    }
}