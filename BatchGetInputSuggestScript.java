package com.sankuai.aitool.runtime.script;

import com.sankuai.aitool.runtime.engine.annotation.Parameter;
import com.sankuai.aitool.runtime.engine.model.JavaEngineCode;
import com.sankuai.aitool.runtime.engine.model.Response;
import com.sankuai.aitool.runtime.engine.rpc.RpcProcessor;
import com.sankuai.aitool.runtime.engine.rpc.impl.RpcRrocessorFactory;
import com.sankuai.aitool.runtime.engine.script.AircraftScriptExcutor;
import com.sankuai.aitool.runtime.engine.util.JacksonUtil;
import com.sankuai.aitool.runtime.engine.util.json.FreeMarkerParser;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BatchGetInputSuggestScript implements AircraftScriptExcutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AircraftScriptExcutor.class);
    private static final String LOG_PREFIX = "[AC_Java_BatchGetInputSuggest] ";
    private static final int SUGGEST_TOP_K = 5;

    @Override
    public void excutor(String paramJson, Response response) {
        try {
            LOGGER.info("{} start processing, paramJson = {}", LOG_PREFIX, paramJson);
            
            // 解析输入参数
            String key = FreeMarkerParser.parse(paramJson, "key");
            String city = FreeMarkerParser.parse(paramJson, "city");
            String location = FreeMarkerParser.parse(paramJson, "location");
            
            // 解析keywords列表
            List<String> keywords = FreeMarkerParser.convert2ListByPath(paramJson, "keywords", String.class);
            
            LOGGER.info("{} parameters: key={}, city={}, location={}, keywords size={}", 
                    LOG_PREFIX, key, city, location, keywords != null ? keywords.size() : 0);
            
            // 检查参数
            if (keywords == null || keywords.isEmpty()) {
                OutputParam outputParam = new OutputParam();
                outputParam.setResultMap(new HashMap<>());
                response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
                return;
            }
            
            // 批量处理每个关键词
            Map<String, List<SuggestResult>> resultMap = new HashMap<>();
            
            for (String keyword : keywords) {
                try {
                    List<SuggestResult> suggestResults = processKeyword(key, keyword, city, location);
                    resultMap.put(keyword, suggestResults);
                } catch (Exception e) {
                    LOGGER.error("{} process keyword failed, keyword={}, e={}", LOG_PREFIX, keyword, e.getMessage());
                    // 为失败的关键词返回空列表
                    resultMap.put(keyword, new ArrayList<>());
                }
            }
            
            // 组装返回结果
            OutputParam outputParam = new OutputParam();
            outputParam.setResultMap(resultMap);

            // 设置返回值
            response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
            LOGGER.info("{} response = {}", LOG_PREFIX, JacksonUtil.toJsonStrWithEmptyDefault(response));
        } catch (Exception e) {
            LOGGER.error("{} invoke failed e = {}", LOG_PREFIX, e);
            response.setJavaEngineCode(JavaEngineCode.SERVER_INNER_ERROR, e.getMessage());
        }
    }
    
    private List<SuggestResult> processKeyword(String key, String keyword, String city, String location) throws Exception {
        LOGGER.info("{} processing keyword: {}", LOG_PREFIX, keyword);
        
        // 1.设置Thrift接口入参类型列表
        List<String> parameterType = new ArrayList<>();
        parameterType.add("mtmap.geoinfo.geoinfo_base.SuggestRequest");

        // 2.构造SuggestRequest对象
        Map<String, Object> suggestRequest = new HashMap<>();
        suggestRequest.put("key", key);
        suggestRequest.put("keywords", keyword);
        suggestRequest.put("city", city);
        suggestRequest.put("location", location);
        
        // 3.设置Thrift接口入参值列表
        List<String> parameterArgs = new ArrayList<>();
        parameterArgs.add(JacksonUtils.simpleSerialize(suggestRequest));

        // 4.获取Thrift接口实例
        RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                "com.sankuai.map.open.platform.api.MapOpenApiService", 
                "com.sankuai.apigw.map.facadecenter", 
                5000, 
                null, 
                null);

        // 5.调用Thrift接口
        String jsonString = rpcProcessor.invoke("suggest", parameterType, parameterArgs);
        
        LOGGER.info("{} thrift response for keyword {}: {}", LOG_PREFIX, keyword, jsonString);
        
        // 6.解析返回结果
        List<SuggestResult> inputSuggestList = new ArrayList<>();
        
        try {
            // 使用Jackson解析返回的SuggestResponse
            Map suggestResponse = JacksonUtils.simpleDeserialize(jsonString, Map.class);
            
            if (suggestResponse != null && suggestResponse.containsKey("suginfos")) {
                List suginfos = (List) suggestResponse.get("suginfos");
                
                if (suginfos != null) {
                    int size = Math.min(SUGGEST_TOP_K, suginfos.size());
                    for (int i = 0; i < size; i++) {
                        Map suggestInfo = (Map) suginfos.get(i);
                        if (suggestInfo != null) {
                            SuggestResult result = new SuggestResult();
                            result.setName(getStringValue(suggestInfo, "name"));
                            result.setNewType(getStringValue(suggestInfo, "new_type"));
                            result.setType(getStringValue(suggestInfo, "type"));
                            result.setLocation(getStringValue(suggestInfo, "location"));
                            result.setAddress(getStringValue(suggestInfo, "address"));
                            result.setCity(getStringValue(suggestInfo, "city"));
                            result.setCityCode(getStringValue(suggestInfo, "citycode"));
                            
                            inputSuggestList.add(result);
                        }
                    }
                }
            }
        } catch (Exception parseException) {
            LOGGER.warn("{} parse suggest response failed for keyword {}: {}", 
                    LOG_PREFIX, keyword, parseException.getMessage());
        }
        
        LOGGER.info("{} keyword {} processed, result size: {}", LOG_PREFIX, keyword, inputSuggestList.size());
        return inputSuggestList;
    }
    
    private String getStringValue(Map map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    @Data
    public static class InputParam {
        @Parameter(name="服务权限标识")
        private String key;
        
        @Parameter(name="搜索关键词列表")
        private List<String> keywords;
        
        @Parameter(name="城市")
        private String city;
        
        @Parameter(name="坐标")
        private String location;
    }

    @Data
    public static class OutputParam {
        @Parameter(name="批量搜索结果")
        private Map<String, List<SuggestResult>> resultMap;
    }
    
    @Data
    public static class SuggestResult {
        @Parameter(name="名称")
        private String name;
        
        @Parameter(name="新类型")
        private String newType;
        
        @Parameter(name="类型")
        private String type;
        
        @Parameter(name="位置坐标")
        private String location;
        
        @Parameter(name="地址")
        private String address;
        
        @Parameter(name="城市")
        private String city;
        
        @Parameter(name="城市编码")
        private String cityCode;
    }
} 