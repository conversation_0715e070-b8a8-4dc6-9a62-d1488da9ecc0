package com.sankuai.aitool.runtime.script;

import com.sankuai.aitool.runtime.engine.annotation.Parameter;
import com.sankuai.aitool.runtime.engine.model.JavaEngineCode;
import com.sankuai.aitool.runtime.engine.model.Response;
import com.sankuai.aitool.runtime.engine.rpc.RpcProcessor;
import com.sankuai.aitool.runtime.engine.rpc.impl.RpcRrocessorFactory;
import com.sankuai.aitool.runtime.engine.script.AircraftScriptExcutor;
import com.sankuai.aitool.runtime.engine.util.JacksonUtil;
import com.sankuai.aitool.runtime.engine.util.json.FreeMarkerParser;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class MapRouteGuideScript implements AircraftScriptExcutor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AircraftScriptExcutor.class);
    private static final String LOG_PREFIX = "[AC_Java_MapRouteGuide] ";

    private static final String DRIVING_TYPE = "driving";
    private static final String WALKING_TYPE = "walking";
    private static final String TRANSIT_TYPE = "transit";

    @Override
    public void excutor(String paramJson, Response response) {
        try {
            LOGGER.info("{} start processing, paramJson = {}", LOG_PREFIX, paramJson);
            
            // 解析输入参数
            String origin = FreeMarkerParser.parse(paramJson, "origin");
            String destination = FreeMarkerParser.parse(paramJson, "destination");
            List<String> types = FreeMarkerParser.convert2ListByPath(paramJson, "types", String.class);
            
            // 参数校验
            if (origin == null || origin.trim().isEmpty() || 
                destination == null || destination.trim().isEmpty() || 
                types == null || types.isEmpty()) {
                LOGGER.warn("{} invalid parameters: origin={}, destination={}, types={}", 
                    LOG_PREFIX, origin, destination, types);
                OutputParam outputParam = new OutputParam();
                outputParam.setRouteResults(new ArrayList<>());
                response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
                return;
            }

            List<RouteGuideResult> results = new ArrayList<>();
            
            // 根据类型获取相应的路线
            if (types.contains(DRIVING_TYPE)) {
                addDrivingRoute(results, origin, destination);
            }
            if (types.contains(WALKING_TYPE)) {
                addWalkingRoute(results, origin, destination);
            }
            if (types.contains(TRANSIT_TYPE)) {
                addTransitRoute(results, origin, destination);
            }

            // 设置返回值
            OutputParam outputParam = new OutputParam();
            outputParam.setRouteResults(results);
            response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
            LOGGER.info("{} response = {}", LOG_PREFIX, JacksonUtil.toJsonStrWithEmptyDefault(response));
            
        } catch (Exception e) {
            LOGGER.error("{} invoke failed e = {}", LOG_PREFIX, e);
            response.setJavaEngineCode(JavaEngineCode.SERVER_INNER_ERROR);
        }
    }

    private void addDrivingRoute(List<RouteGuideResult> results, String origin, String destination) {
        try {
            // 1.设置Thrift接口入参类型列表
            List<String> parameterType = new ArrayList<>();
            parameterType.add("com.sankuai.map.open.platform.api.driving.DrivingRouteRequest");

            // 2.构造驾车路线请求参数
            Map<String, Object> drivingRequest = new HashMap<>();
            
            Map<String, Object> originMap = new HashMap<>();
            originMap.put("location", origin);
            drivingRequest.put("origin", originMap);
            
            Map<String, Object> destinationMap = new HashMap<>();
            destinationMap.put("location", destination);
            drivingRequest.put("destination", destinationMap);
            
            drivingRequest.put("show_fields", "duration");

            List<String> parameterArgs = new ArrayList<>();
            parameterArgs.add(JacksonUtils.simpleSerialize(drivingRequest));

            // 3.获取Thrift接口实例
            RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                    "com.sankuai.map.open.platform.api.MapOpenApiService", 
                    "com.sankuai.apigw.map.facadecenter", 
                    5000, 
                    null, 
                    null);

            // 4.调用Thrift接口
            String jsonString = rpcProcessor.invoke("driving", parameterType, parameterArgs);
            
            LOGGER.info("{} driving response = {}", LOG_PREFIX, jsonString);

            // 5.解析返回结果
            RouteGuideResult routeGuideResult = new RouteGuideResult();
            routeGuideResult.setType(DRIVING_TYPE);
            
            List<RouteResult> routeResults = new ArrayList<>();
            List<Map> routes = FreeMarkerParser.convert2ListByPath(jsonString, "route", Map.class);
            
            if (routes != null && !routes.isEmpty()) {
                for (Map route : routes) {
                    RouteResult routeResult = new RouteResult();
                    
                    routeResult.setOrigin(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "origin"));
                    routeResult.setDestination(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "destination"));
                    
                    Double distance = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(route), "distance", 0.0);
                    Integer duration = FreeMarkerParser.parseInt(JacksonUtils.simpleSerialize(route), "duration", 0);
                    
                    routeResult.setDistance(distance);
                    routeResult.setDuration(duration.doubleValue());
                    
                    routeResults.add(routeResult);
                }
            }
            
            routeGuideResult.setRouteResults(routeResults);
            results.add(routeGuideResult);
            
        } catch (Exception e) {
            LOGGER.error("{} addDrivingRoute failed", LOG_PREFIX, e);
        }
    }

    private void addWalkingRoute(List<RouteGuideResult> results, String origin, String destination) {
        try {
            // 1.设置Thrift接口入参类型列表
            List<String> parameterType = new ArrayList<>();
            parameterType.add("com.sankuai.wmarch.map.thriftClient.route.RouteRequest");

            // 2.构造步行路线请求参数
            Map<String, Object> walkingRequest = new HashMap<>();
            
            Map<String, Object> originMap = new HashMap<>();
            originMap.put("location", origin);
            walkingRequest.put("origin", originMap);
            
            Map<String, Object> destinationMap = new HashMap<>();
            destinationMap.put("location", destination);
            walkingRequest.put("destination", destinationMap);
            
            walkingRequest.put("show_fields", "duration");

            List<String> parameterArgs = new ArrayList<>();
            parameterArgs.add(JacksonUtils.simpleSerialize(walkingRequest));

            // 3.获取Thrift接口实例
            RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                    "com.sankuai.map.open.platform.api.MapOpenApiService", 
                    "com.sankuai.apigw.map.facadecenter", 
                    5000, 
                    null, 
                    null);

            // 4.调用Thrift接口
            String jsonString = rpcProcessor.invoke("walking", parameterType, parameterArgs);
            
            LOGGER.info("{} walking response = {}", LOG_PREFIX, jsonString);

            // 5.解析返回结果
            RouteGuideResult routeGuideResult = new RouteGuideResult();
            routeGuideResult.setType(WALKING_TYPE);
            
            List<RouteResult> routeResults = new ArrayList<>();
            List<Map> routes = FreeMarkerParser.convert2ListByPath(jsonString, "route", Map.class);
            
            if (routes != null && !routes.isEmpty()) {
                for (Map route : routes) {
                    RouteResult routeResult = new RouteResult();
                    
                    routeResult.setOrigin(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "origin"));
                    routeResult.setDestination(FreeMarkerParser.parse(JacksonUtils.simpleSerialize(route), "destination"));
                    
                    Double distance = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(route), "distance", 0.0);
                    Double duration = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(route), "duration", 0.0);
                    
                    routeResult.setDistance(distance);
                    routeResult.setDuration(duration);
                    
                    routeResults.add(routeResult);
                }
            }
            
            routeGuideResult.setRouteResults(routeResults);
            results.add(routeGuideResult);
            
        } catch (Exception e) {
            LOGGER.error("{} addWalkingRoute failed", LOG_PREFIX, e);
        }
    }

    private void addTransitRoute(List<RouteGuideResult> results, String origin, String destination) {
        try {
            // 1.设置Thrift接口入参类型列表
            List<String> parameterType = new ArrayList<>();
            parameterType.add("com.sankuai.map.open.platform.api.transit.TransitRouteRequest");

            // 2.构造公交路线请求参数
            Map<String, Object> transitRequest = new HashMap<>();
            
            Map<String, Object> originMap = new HashMap<>();
            originMap.put("location", origin);
            transitRequest.put("origin", originMap);
            
            Map<String, Object> destinationMap = new HashMap<>();
            destinationMap.put("location", destination);
            transitRequest.put("destination", destinationMap);
            
            // 非批量模式使用 STRATEGY_LESS_TIME 策略
            transitRequest.put("strategy", "STRATEGY_LESS_TIME");

            List<String> parameterArgs = new ArrayList<>();
            parameterArgs.add(JacksonUtils.simpleSerialize(transitRequest));

            // 3.获取Thrift接口实例
            RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                    "com.sankuai.map.open.platform.api.MapOpenApiService", 
                    "com.sankuai.apigw.map.facadecenter", 
                    5000, 
                    null, 
                    null);

            // 4.调用Thrift接口
            String jsonString = rpcProcessor.invoke("transit", parameterType, parameterArgs);
            
            LOGGER.info("{} transit response = {}", LOG_PREFIX, jsonString);

            // 5.解析返回结果
            RouteGuideResult routeGuideResult = new RouteGuideResult();
            routeGuideResult.setType(TRANSIT_TYPE);
            
            List<Map> routes = FreeMarkerParser.convert2ListByPath(jsonString, "routes", Map.class);
            
            if (routes != null && !routes.isEmpty()) {
                // 非批量模式只取第一条路线
                Map firstRoute = routes.get(0);
                
                RouteResult routeResult = new RouteResult();
                routeResult.setOrigin(origin);
                routeResult.setDestination(destination);
                
                Double distance = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(firstRoute), "distance", 0.0);
                Double duration = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(firstRoute), "duration", 0.0);
                
                routeResult.setDistance(distance);
                routeResult.setDuration(duration);
                
                // 处理公交换乘信息
                List<Map> segments = FreeMarkerParser.convert2ListByPath(JacksonUtils.simpleSerialize(firstRoute), "segments", Map.class);
                if (segments != null && !segments.isEmpty()) {
                    TransitInfo transitInfo = generateTransitInfo(segments);
                    routeResult.setTransitInfo(transitInfo);
                }
                
                routeGuideResult.setRouteResults(Arrays.asList(routeResult));
            } else {
                routeGuideResult.setRouteResults(new ArrayList<>());
            }
            
            results.add(routeGuideResult);
            
        } catch (Exception e) {
            LOGGER.error("{} addTransitRoute failed", LOG_PREFIX, e);
        }
    }

    private TransitInfo generateTransitInfo(List<Map> segmentsList) {
        try {
            double walkingDistance = 0;
            double walkingDuration = 0;

            for (Map segmentWrapper : segmentsList) {
                List<Map> segmentList = FreeMarkerParser.convert2ListByPath(JacksonUtils.simpleSerialize(segmentWrapper), "segment", Map.class);
                if (segmentList == null || segmentList.isEmpty()) {
                    continue;
                }
                
                Map segment = segmentList.get(0);
                if (segment == null) {
                    continue;
                }

                Integer type = FreeMarkerParser.parseInt(JacksonUtils.simpleSerialize(segment), "type", 0);
                
                if (type == 3) { // 步行
                    Double distance = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(segment), "distance", 0.0);
                    Double duration = FreeMarkerParser.parseDouble(JacksonUtils.simpleSerialize(segment), "duration", 0.0);
                    walkingDistance += distance;
                    walkingDuration += duration;
                } else { // 公交或地铁
                    TransitInfo transitInfo = new TransitInfo();
                    transitInfo.setTransitType(type);
                    transitInfo.setWalkingDistance(walkingDistance);
                    transitInfo.setWalkingDuration(walkingDuration);
                    
                    // 获取线路信息
                    String lineName = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(segment), "line_info.name");
                    transitInfo.setTransitName(lineName);
                    
                    // 获取站点信息
                    List<Map> stations = FreeMarkerParser.convert2ListByPath(JacksonUtils.simpleSerialize(segment), "stations", Map.class);
                    if (stations != null && !stations.isEmpty()) {
                        String stationName = FreeMarkerParser.parse(JacksonUtils.simpleSerialize(stations.get(0)), "name");
                        transitInfo.setStationName(stationName);
                    }
                    
                    return transitInfo;
                }
            }
            
        } catch (Exception e) {
            LOGGER.error("{} generateTransitInfo failed", LOG_PREFIX, e);
        }
        
        return null;
    }

    @Data
    public static class InputParam {
        @Parameter(name="起始点坐标")
        private String origin;
        
        @Parameter(name="终点坐标")
        private String destination;
        
        @Parameter(name="路线类型列表")
        private List<String> types;
    }

    @Data
    public static class OutputParam {
        @Parameter(name="路线规划结果列表")
        private List<RouteGuideResult> routeResults;
    }
    
    @Data
    public static class RouteGuideResult {
        @Parameter(name="路线类型")
        private String type;
        
        @Parameter(name="路线结果列表")
        private List<RouteResult> routeResults;
    }
    
    @Data
    public static class RouteResult {
        @Parameter(name="起始点")
        private String origin;
        
        @Parameter(name="终点")
        private String destination;
        
        @Parameter(name="用时（秒）")
        private Double duration;
        
        @Parameter(name="距离（米）")
        private Double distance;
        
        @Parameter(name="公交信息")
        private TransitInfo transitInfo;
    }
    
    @Data
    public static class TransitInfo {
        @Parameter(name="交通类型")
        private Integer transitType;
        
        @Parameter(name="交通工具名称")
        private String transitName;
        
        @Parameter(name="站点名称")
        private String stationName;
        
        @Parameter(name="步行距离")
        private Double walkingDistance;
        
        @Parameter(name="步行用时")
        private Double walkingDuration;
    }
}
