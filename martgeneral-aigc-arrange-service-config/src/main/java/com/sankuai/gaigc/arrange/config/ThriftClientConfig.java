package com.sankuai.gaigc.arrange.config;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamQueryThriftService;
import com.meituan.service.wpt.horizon.server.api.HorizonCacheService;
import com.sankuai.solarsystem.aigc.basicapi.client.service.IContentEmbeddingThriftService;
import com.sankuai.solarsystem.aigc.basicapi.client.service.IHorusImgVideoEmbeddingService;
import com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import org.springframework.context.annotation.Configuration;
import com.meituan.nibhtp.os.htp.cms.access.thrift.service.common.JsonContentThriftService;

@Configuration
public class ThriftClientConfig {
    /**
     * 学城操作接口
     * */
    @MdpThriftClient(remoteAppKey = "com.sankuai.dxenterprise.open.gateway", timeout = 5000)
    private XmOpenKmServiceI.Iface xmKmService;
    /**
     * 学城鉴权接口
     * */
    @MdpThriftClient(remoteAppKey = "com.sankuai.dxenterprise.open.gateway", timeout = 3000)
    private XmAuthServiceI.Iface xmAuthService;
    /**
     * horizon实验接口
     * */
    @MdpThriftClient(remoteAppKey = "com.sankuai.wpt.horizon.horizonserver", timeout = 20000)
    private HorizonCacheService.Iface horizonCacheService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.solarsystem.aigc.basicapi", timeout = 10000)
    private IContentEmbeddingThriftService embeddingThriftService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.solarsystem.aigc.basicapi", remoteServerPort = 9000, timeout = 60000)
    private IHorusImgVideoEmbeddingService horusImgVideoEmbeddingService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.cms.knowledge.build", timeout = 10000)
    private IDataStreamQueryThriftService dataStreamQueryThriftService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.cms.query", timeout = 10000, remoteServerPort = 8415)
    private JsonContentThriftService jsonContentThriftService;
}
