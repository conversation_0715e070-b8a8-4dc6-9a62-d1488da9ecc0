package com.sankuai.gaigc.arrange.config;

import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.gaigc.arrange.config.mccconfig.ComponentModelDegradeConfig;
import com.sankuai.gaigc.arrange.config.mccconfig.GeneralLLMGrayConfig;
import com.sankuai.gaigc.arrange.config.mccconfig.SimpleDeserializerHandler;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;

/**
 * <AUTHOR>
 * date 2024/5/14
 */
@Component
@Getter
public class MccConfig {
    @MdpConfig("PromptOptimizeBotId")
    private Long promptOptimizeBotId;
    @MdpConfig("PromptDiagnoseBotId")
    private Long promptDiagnoseBotId;
    @MdpConfig("bot-plan-model-name")
    private String botPlanModelName;
    @MdpConfig("bot-reply-model-name")
    private String botReplyModelName;
    @MdpConfig("bot-function-call-model-name")
    private String botFunctionCallModelName;
    @MdpConfig("week-report-xm-parent-id")
    private String weekReportXmParentId;
    @MdpConfig("week-report-operator-emp-Id")
    private Long weekReportOperatorEmpId;
    @MdpConfig(value = "component-model-degrade-config", deserializer = SimpleDeserializerHandler.class)
    private ComponentModelDegradeConfig componentModelDegradeConfig;
    /** 支持json_object的模型名称 */
    @MdpConfig("support-json-object-models")
    private HashSet<String> supportJsonObjectModels = Sets.newHashSet();
    /** 是否使用 FuxiFridayChatLanguageModel */
    @MdpConfig("use-fuxi-friday-model")
    private Boolean useFuxiFridayChatLanguageModel;
    @MdpConfig("bot-chat-message-compress-flowid")
    private Long chatMessageCompressFlowId;
    /** 知识库中台知识库ID */
    @MdpConfig("kcube-knowledge-base-id")
    private Long kCubeKnowledgeBaseId;
    //门票购票助手相关插件对应的flowIds
    @MdpConfig("ticket-info-plugin-flow-ids")
    private HashMap<String, Long> ticketInfoPluginFlowIds;
    @MdpConfig("demestic-hotel-bot-id")
    private Long demesticHotelBotId;
    /** 内容推荐去重阈值 */
    @MdpConfig("query-recommend-distinct-threshold")
    private Double distinctThreshold = 0.8D;
    /**
     * 是否启用保时洁审核
     */
    @MdpConfig("com.sankuai.gaigc.arrange.service.is-enable-bao-shi-jie-input-audit")
    private Boolean isEnableBaoShiJieInputAudit = false;
    @MdpConfig("flow-engin-run-max-timeout-mills")
    private Long flowEnginRunMaxTimeOutMills = 3 * 60 * 1000L;
    @MdpConfig(value = "general-llm-gray-config", deserializer = SimpleDeserializerHandler.class)
    private GeneralLLMGrayConfig generalLLMGrayConfig;
    /**
     * true代表调用新的意图识别方法,false代表调用旧的意图识别方法
     */
    @MdpConfig("is-call-new-hotel-intent-cls-method")
    private Boolean isCallNewHotelIntentClsMethod = false;
    @MdpConfig("bot-prologue-problem-flow-old")
    private HashSet<Long> botPrologueProblemFlowOld = Sets.newHashSet();
    /**
     * 不走缓存的列表
     */
    @MdpConfig("not-enable-cache-flow-ids")
    private HashSet<Long> notEnableCacheFlowIds = Sets.newHashSet();
    /**
     * 是否启用flow快照缓存
     */
    @MdpConfig("is-enable-flow-snapshot-cache")
    private Boolean isEnableFlowSnapshotCache = false;
    @MdpConfig("create-dts-aigc-flow-snapshot-param")
    private String createDtsAigcFlowSnapshotParam;
}


